using System;
using System.Threading.Tasks;

namespace XJ.Framework.Library.Cache.Abstraction;

/// <summary>
/// 缓存接口
/// </summary>
public interface ICache
{
    /// <summary>
    /// 获取缓存项
    /// </summary>
    /// <typeparam name="T">缓存项类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <returns>缓存项</returns>
    Task<T?> GetAsync<T>(string key);

    /// <summary>
    /// 设置缓存项
    /// </summary>
    /// <typeparam name="T">缓存项类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <param name="value">缓存值</param>
    /// <param name="expiration">过期时间</param>
    Task SetAsync<T>(string key, T value, TimeSpan expiration);

    /// <summary>
    /// 移除缓存项
    /// </summary>
    /// <param name="key">缓存键</param>
    Task RemoveAsync(string key);

    /// <summary>
    /// 获取或添加缓存项
    /// </summary>
    /// <typeparam name="T">缓存项类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <param name="factory">缓存值工厂方法</param>
    /// <param name="expiration">过期时间</param>
    /// <returns>缓存项</returns>
    Task<T> GetOrAddAsync<T>(string key, Func<Task<T>> factory, TimeSpan expiration);

    /// <summary>
    /// 检查缓存项是否存在
    /// </summary>
    /// <param name="key">缓存键</param>
    /// <returns>是否存在</returns>
    Task<bool> ExistsAsync(string key);

    /// <summary>
    /// 批量移除缓存项
    /// </summary>
    /// <param name="keys">缓存键集合</param>
    Task RemoveAllAsync(params string[] keys);

    /// <summary>
    /// 使用模式匹配移除缓存项
    /// </summary>
    /// <param name="pattern">模式</param>
    Task RemoveByPatternAsync(string pattern);

    /// <summary>
    /// 清空所有缓存
    /// </summary>
    Task ClearAsync();

    /// <summary>
    /// 刷新缓存项过期时间
    /// </summary>
    /// <param name="key">缓存键</param>
    /// <param name="expiration">新的过期时间</param>
    Task RefreshAsync(string key, TimeSpan expiration);
} 