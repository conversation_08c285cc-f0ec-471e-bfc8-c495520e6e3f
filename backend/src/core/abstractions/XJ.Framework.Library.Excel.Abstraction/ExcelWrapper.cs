namespace XJ.Framework.Library.Excel.Abstraction;

public abstract class ExcelWrapper<TWorkbook, TSheet, TRow, TCell, TCellStyle>
{
    // protected readonly string FilePath;
    //
    // protected readonly TWorkbook Workbook;
    //
    // protected ExcelWrapper(string filePath)
    // {
    //     FilePath = filePath;
    //     Workbook = GetWorkBook();
    // }

    public List<Dictionary<string, object>> ReadSheetData(TSheet sheet, int beginColumnNum, int beginRowNum,
        Func<TRow, bool> emptyFilter)
    {
        var beginRow = GetOrCreateRow(sheet, beginRowNum);
        var lastRowNum = GetLastRowNum(sheet);
        var lastCellNum = GetLastCellNum(beginRow);

        var columns = new Dictionary<int, string>();


        var cells = GetCells(beginRow).ToList();

        cells.ForEach(cell =>
        {
            var cellIndex = GetCellIndex(cell);
            if (cellIndex >= beginColumnNum && cellIndex <= lastCellNum)
                columns.Add(cellIndex, GetCellStringValue(cell));
        });

        var query = Enumerable.Range(beginRowNum + 1, lastRowNum).AsParallel();

        var data = query.Select(rowNum =>
        {
            var row = GetOrCreateRow(sheet, rowNum);

            Dictionary<string, object>? item = null;

            if (!emptyFilter(row))
            {
                return item;
            }

            item = new Dictionary<string, object> { { "row_num", rowNum } };

            foreach (var columnsKey in columns.Keys)
            {
                var cell = GetOrCreateCell(row, columnsKey);

                var cellValue = GetCellStringValue(cell);

                item.Add(columns[columnsKey], cellValue);
            }

            return item;
        });

        return data.Where(row => row != null).ToList()!;
    }


    public abstract TWorkbook GetWorkBook(MemoryStream stream);


    #region Sheet

    public abstract IEnumerable<TSheet> GetSheets(TWorkbook workbook, bool filterVisible = true);

    public abstract TSheet GetSheet(TRow row);

    public abstract TSheet GetSheet(TCell cell);

    public abstract TSheet SetColumnWidthUnit(TSheet sheet, int columnIndex, int columnWidth);

    public abstract TSheet SetDefaultTableStyle(TSheet sheet, int beginRow, int beginColumn, int headerCount,
        int dataCount,
        int tableWidth);

    public abstract bool IsSheetHidden(TWorkbook workbook, int sheetIndex);

    #endregion

    #region CellStyle

    public abstract TCellStyle GetHeaderCellStyle(TSheet sheet);

    public abstract TCellStyle GetDataCellStyle(TSheet sheet);

    #endregion

    #region Row

    public abstract TRow? GetRow(TSheet sheet, int rowIndex);

    public abstract TRow CreateRow(TSheet sheet, int rowIndex);

    public TRow GetOrCreateRow(TSheet sheet, int rowIndex)
    {
        return GetRow(sheet, rowIndex) ?? CreateRow(sheet, rowIndex);
    }

    public abstract TRow GetRow(TCell cell);

    public abstract int GetRowIndex(TRow row);

    public abstract int GetLastRowNum(TSheet sheet);

    #endregion

    #region Cell

    public abstract Tuple<int, int> GetCellAddress(string cellAddress);

    public abstract int GetSheetCount(TWorkbook workbook);

    public abstract IEnumerable<TCell> GetCells(TRow row);

    public abstract TCell? GetCell(TRow row, int cellIndex);

    public abstract TCell CreateCell(TRow row, int cellIndex);

    public TCell GetOrCreateCell(TRow row, int cellIndex)
    {
        return GetCell(row, cellIndex) ?? CreateCell(row, cellIndex);
    }

    public abstract string GetCellStringValue(TSheet sheet, string cellAddress);

    public abstract string GetCellStringValue(TRow row, int cellIndex);

    public abstract string GetCellStringValue(TCell cell);

    public abstract TCell SetWrapText(TCell cell, bool wrap);

    public abstract TCell SetMergeRange(TCell cell, int rowCount, int columnCount);

    public abstract TCell SetMergeRange(TSheet sheet, int firstRow, int lastRow, int firstCol, int lastCol);

    public abstract TCell SetCellStyle(TCell cell, TCellStyle cellStyle);

    public abstract int GetCellIndex(TCell cell);

    public abstract int GetLastCellNum(TRow row);

    #endregion
}