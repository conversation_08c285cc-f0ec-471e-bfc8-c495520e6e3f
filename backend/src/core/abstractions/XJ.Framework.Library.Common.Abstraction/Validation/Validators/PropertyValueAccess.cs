using System.Reflection;

namespace XJ.Framework.Library.Common.Abstraction.Validation.Validators;

public class PropertyValueAccess : ValueAccess
{
    private readonly PropertyInfo propertyInfo;

    public PropertyValueAccess(PropertyInfo pi)
    {
        this.propertyInfo = pi;
    }

    public override object? GetValue(object target)
    {
        return this.propertyInfo.GetValue(target, null);
    }
}