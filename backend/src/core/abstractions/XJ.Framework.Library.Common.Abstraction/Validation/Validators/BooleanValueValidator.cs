namespace XJ.Framework.Library.Common.Abstraction.Validation.Validators;

internal class BooleanValueValidator : Validator
{
    public BooleanValueValidator(string messageTemplate, bool targetValue, string tag)
        : base(messageTemplate, tag)
    {
        this.TargetValue = targetValue;
    }

    public bool TargetValue {
        get;
        set;
    }

    public override void DoValidate(object? objectToValidate, object currentObject, string? key,
        ValidationResults validateResults)
    {
        if (objectToValidate != null && objectToValidate is bool)
        {
            var source = (bool)objectToValidate;

            if (source != this.TargetValue)
                this.RecordValidationResult(validateResults, this.MessageTemplate, currentObject, key);
        }
    }
}