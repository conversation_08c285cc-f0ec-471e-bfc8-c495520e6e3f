namespace XJ.Framework.Library.Common.Abstraction.Validation.Validators;

public abstract class NumberRangeValidatorBase<T> : Validator where T : IComparable
{
    private T lowerBound;

    /// <summary>
    /// 下限
    /// </summary>
    public virtual T LowerBound {
        get { return lowerBound; }
        set { lowerBound = value; }
    }

    private T upperBound;

    /// <summary>
    /// 上限
    /// </summary>
    public virtual T UpperBound {
        get { return upperBound; }
        set { upperBound = value; }
    }

    public NumberRangeValidatorBase(T lowerBound, T upperBound)
        : base(string.Empty, string.Empty)
    {
        this.lowerBound = lowerBound;
        this.upperBound = upperBound;
    }

    public NumberRangeValidatorBase(T lowerBound, T upperBound, string messageTemplate, string tag)
        : base(messageTemplate, tag)
    {
        this.lowerBound = lowerBound;
        this.upperBound = upperBound;
    }

    public override void DoValidate(
        object? objectToValidate,
        object currentTarget,
        string? key,
        ValidationResults validationResults)
    {
        var isValid = false;

        if (objectToValidate != null)
        {
            RangeChecker<T> checker = new(this.lowerBound, this.upperBound);
            isValid = checker.IsInRange((T)objectToValidate);
        }

        if (isValid == false)
            RecordValidationResult(validationResults, this.MessageTemplate, currentTarget, key);
    }
}