using System.Security.Cryptography;
using System.Text;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Cryptography
{
    /// <summary>
    /// 基于DES的对称算法的基类
    /// </summary>
    public abstract class DESEncryptorBase : ISymmetricEncryption
    {
        /// <summary>
        /// 将二进制流加密为字符串
        /// </summary>
        /// <param name="strData"></param>
        /// <returns></returns>
        public byte[] EncryptString(string strData)
        {
            using (SymmetricAlgorithm algorithm = this.GetDesObject())
            {
                return this.EncryptString(strData, algorithm);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="strData"></param>
        /// <param name="algorithm"></param>
        /// <returns></returns>
        public byte[] EncryptString(string strData, SymmetricAlgorithm algorithm)
        {
            strData.CheckStringIsNullOrEmpty(nameof(strData));
            algorithm.NullCheck(nameof(algorithm));

            var bytes = Encoding.UTF8.GetBytes(strData);

            var ms = new MemoryStream();

            using (var transform = algorithm.CreateEncryptor())
            {
                using (var encStream = new CryptoStream(ms, transform, CryptoStreamMode.Write))
                {
                    encStream.Write(bytes, 0, bytes.Length);
                }
            }

            return ms.ToArray();
        }

        /// <summary>
        /// 将二进制流解密为字符串
        /// </summary>
        /// <param name="encryptedData"></param>
        /// <returns></returns>
        public string DecryptString(byte[] encryptedData)
        {
            using (SymmetricAlgorithm algorithm = this.GetDesObject())
            {
                return this.DecryptString(encryptedData, algorithm);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="encryptedData"></param>
        /// <param name="algorithm"></param>
        /// <returns></returns>
        public string DecryptString(byte[] encryptedData, SymmetricAlgorithm algorithm)
        {
            encryptedData.NullCheck(nameof(encryptedData));
            algorithm.NullCheck(nameof(algorithm));

            var strResult = string.Empty;

            var ms = new MemoryStream();

            ms.Write(encryptedData, 0, encryptedData.Length);
            ms.Seek(0, SeekOrigin.Begin);

            using (var transform = algorithm.CreateDecryptor())
            {
                using (var decStream = new CryptoStream(ms, transform, CryptoStreamMode.Read))
                {
                    strResult = (new StreamReader(decStream, Encoding.UTF8)).ReadToEnd();
                }
            }

            return strResult;
        }

        /// <summary>
        /// 得到DES对象（提供密钥）
        /// </summary>
        /// <returns></returns>
        protected abstract DES GetDesObject();
    }
}