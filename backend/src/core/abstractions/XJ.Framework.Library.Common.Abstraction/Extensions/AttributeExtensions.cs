using XJ.Framework.Library.Common.Abstraction.Attributes;
using XJ.Framework.Library.Common.Abstraction.Exceptions;

namespace XJ.Framework.Library.Common.Abstraction.Extensions;

public static class AttributeExtensions
{
    private static readonly Dictionary<Type, List<TargetTypeAttribute>> innerTypeDictionary = [];

    private static readonly Dictionary<Type, List<TargetDescriptionAttribute>> innerDescriptionDictionary = [];

    public static IList<TargetTypeAttribute> GetTargetTypeAttributes(this System.Type type)
    {
        type.NullCheck(nameof(type));

        return innerTypeDictionary.GetOrAddValueWithLock(type, typeInCache =>
        {
            Attribute[] attrs = Attribute.GetCustomAttributes(typeInCache, typeof(TargetTypeAttribute));

            List<TargetTypeAttribute> result = [];

            attrs.ForEach(attr => result.Add((TargetTypeAttribute)attr));

            return result;
        });
    }

    public static IList<TargetDescriptionAttribute> GetTargetDescriptionAttributes(this System.Type type)
    {
        type.NullCheck(nameof(type));

        return innerDescriptionDictionary.GetOrAddValueWithLock(type, typeInCache =>
        {
            Attribute[] attrs = Attribute.GetCustomAttributes(type, typeof(TargetDescriptionAttribute));

            List<TargetDescriptionAttribute> result = [];

            attrs.ForEach(attr => result.Add((TargetDescriptionAttribute)attr));

            return result;
        });
    }

    /// <summary>
    /// sourceType这个类型是否支持TTargetType类型
    /// </summary>
    /// <typeparam name="TTargetType"></typeparam>
    /// <param name="sourceType"></param>
    /// <returns></returns>
    public static bool IsMatchedTargetType<TTargetType>(this System.Type sourceType)
    {
        return sourceType.IsMatchedTargetType(typeof(TTargetType));
    }

    /// <summary>
    /// sourceType这个类型是否支持targetType类型
    /// </summary>
    /// <param name="sourceType"></param>
    /// <param name="targetType"></param>
    /// <returns></returns>
    public static bool IsMatchedTargetType(this System.Type sourceType, System.Type targetType)
    {
        sourceType.NullCheck(nameof(sourceType));
        targetType.NullCheck(nameof(targetType));

        IList<TargetTypeAttribute> supportedTypes = sourceType.GetTargetTypeAttributes();

        var result = false;

        foreach (var supportedTypeAttr in supportedTypes)
        {
            if (targetType == supportedTypeAttr.Target ||
                targetType.IsSubclassOf(supportedTypeAttr.Target))
            {
                result = true;
                break;
            }
        }

        return result;
    }

    /// <summary>
    /// sourceType这个类型是否支持targetType类型
    /// </summary>
    /// <param name="sourceType"></param>
    /// <param name="description"></param>
    /// <returns></returns>
    public static bool IsMatchedTargetDescription(this System.Type sourceType, string description)
    {
        sourceType.NullCheck(nameof(sourceType));
        description.CheckStringIsNullOrEmpty();

        IList<TargetDescriptionAttribute> supportedDesps = sourceType.GetTargetDescriptionAttributes();

        var result = false;

        foreach (var supportedDespAttr in supportedDesps)
        {
            if (description.IgnoreCaseEquals(supportedDespAttr.Target))
            {
                result = true;
                break;
            }
        }

        return result;
    }

    /// <summary>
    /// 在一个集合中，找到匹配的类型处理器。
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="processors"></param>
    /// <param name="types"></param>
    /// <returns></returns>
    public static T FindMatchedProcessor<T>(this IEnumerable<T> processors, params System.Type[] types)
    {
        processors.NullCheck(nameof(processors));

        T? result = default;

        if (types != null)
        {
            foreach (var type in types)
            {
                result = InnerFindMatchedProcessor(processors, type);

                if (result != null)
                    break;
            }
        }

        (result != null).FalseThrow<NotMatchedException>($"不能在\"{processors.GetType()}\"中找到指定类型的处理器");

        return result!;
    }

    /// <summary>
    /// 在一个集合中，找到匹配的类型处理器。
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="processors"></param>
    /// <param name="desps"></param>
    /// <returns></returns>
    public static T FindMatchedProcessor<T>(this IEnumerable<T> processors, params string[] desps)
    {
        processors.NullCheck(nameof(processors));

        T? result = default;

        if (desps != null)
        {
            foreach (var desp in desps)
            {
                result = InnerFindMatchedProcessor(processors, desp);

                if (result != null)
                    break;
            }
        }

        (result != null).FalseThrow<NotMatchedException>($"不能在\"{processors.GetType()}\"找到指定描述的处理器");

        return result!;
    }


    private static T? InnerFindMatchedProcessor<T>(IEnumerable<T> processors, System.Type targetType)
    {
        targetType.NullCheck(nameof(targetType));

        T? result = default;

        if (processors != null)
        {
            foreach (var processor in processors)
            {
                if (processor != null && processor.GetType().IsMatchedTargetType(targetType))
                {
                    result = processor;
                    break;
                }
            }
        }

        return result;
    }

    private static T? InnerFindMatchedProcessor<T>(IEnumerable<T> processors, string description)
    {
        description.CheckStringIsNullOrEmpty();

        T? result = default;

        if (processors != null)
        {
            foreach (var processor in processors)
            {
                if (processor != null && processor.GetType().IsMatchedTargetDescription(description))
                {
                    result = processor;
                    break;
                }
            }
        }

        return result;
    }
}