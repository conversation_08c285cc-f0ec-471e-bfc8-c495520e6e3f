using System.Net;
using System.Text.Json;
using XJ.Framework.Library.Common.Abstraction.Exceptions;
using XJ.Framework.Library.Common.Abstraction.Models;

namespace XJ.Framework.Library.Common.Abstraction.Extensions;

public static class HttpResponseMessageExtensions
{
    public static bool IsSuccessStatusCode(this HttpResponseMessage response)
    {
        response.NullCheck();

        return response.StatusCode >= HttpStatusCode.OK && response.StatusCode < HttpStatusCode.BadRequest;
    }

    /// <summary>
    /// 得到原始的字符串报文，但是会根据参数 ensureSuccessStatusCode 进行校验。
    /// 如果 ensureSuccessStatusCode 为 true，那么返回结果一定是正确的的信息
    /// </summary>
    /// <param name="response"></param>
    /// <param name="ensureSuccessStatusCode"></param>
    /// <returns></returns>
    public static async Task<string> GetStringAsync(this HttpResponseMessage? response,
        bool ensureSuccessStatusCode = true)
    {
        response.NullCheck();

        if (ensureSuccessStatusCode)
            await response.EnsureSuccessAsync();

        return await response!.GetStringDirectlyAsync();
    }

    public static async Task<string> GetStringDataAsync(this HttpResponseMessage? response,
        bool ensureSuccessStatusCode = true)
    {
        response.NullCheck();

        if (ensureSuccessStatusCode)
            await response.EnsureSuccessAsync();

        var content = await response!.GetStringDirectlyAsync();

        return GetDataInContent(content);
    }

    private static string GetDataInContent(string content)
    {
        var result = content;

        try
        {
            if (content.IsNotEmpty())
            {
                Dictionary<string, object> dict = content.FromJson<Dictionary<string, object>>();

                if (dict.ContainsKey("code"))
                {
                    result = dict.GetValue("data", string.Empty);
                }
            }
        }
        catch (JsonException)
        {
        }

        return result;
    }

    public static async Task<ServiceResponse<T>> GetServiceResponseAsync<T>(this HttpResponseMessage? response,
        bool ensureSuccessStatusCode = true)
    {
        var content = await response.GetStringAsync(ensureSuccessStatusCode);

        return content.FromJson<ServiceResponse<T>>();
    }

    public static async Task<T> GetServiceResponseDataAsync<T>(this HttpResponseMessage? response,
        bool ensureSuccessStatusCode = true)
    {
        ServiceResponse<T> serviceResponse = await response.GetServiceResponseAsync<T>(ensureSuccessStatusCode);

        return serviceResponse.Data!;
    }

    public static async Task EnsureSuccessAsync(this HttpResponseMessage? response)
    {
        var ex = await response.ToHttpClientExceptionAsync();

        if (ex != null)
            throw ex;
    }

    public static async Task<System.Exception?> ToHttpClientExceptionAsync(this HttpResponseMessage? response)
    {
        response.NullCheck();

        System.Exception? exception = null;

        if (response!.IsSuccessStatusCode == false)
        {
            var message = await response.GetMessageAsync();

            message = response.RequestMessage.FillRequestInfo(response.StatusCode, message);

            if (response.StatusCode >= HttpStatusCode.BadRequest &&
                response.StatusCode < HttpStatusCode.InternalServerError)
                exception = new HttpClientNonTransientException(message);
            else
                exception = new HttpClientTransientException(message);

            response.RequestMessage.FillRequestInfo((IHttpClientException)exception);
        }

        return exception;
    }

    private static string FillRequestInfo(this HttpRequestMessage? requestMessage, HttpStatusCode statusCode,
        string message)
    {
        var prefix = string.Empty;

        if (requestMessage != null && requestMessage.RequestUri != null)
            prefix = $"{requestMessage.Method} {requestMessage.RequestUri} status code: {statusCode}, exception: ";

        if (prefix.IsNotEmpty())
            message = prefix + message;

        return message;
    }

    private static void FillRequestInfo(this HttpRequestMessage? requestMessage, IHttpClientException exception)
    {
        if (requestMessage != null)
        {
            exception.Method = requestMessage.Method;
            exception.RequestUri = requestMessage.RequestUri;
        }
    }

    private static async Task<string> GetStringDirectlyAsync(this HttpResponseMessage response)
    {
        return await response!.Content.ReadAsStringAsync();
    }

    private static async Task<string> GetMessageAsync(this HttpResponseMessage response)
    {
        var content = await response!.Content.ReadAsStringAsync();

        var message = content;

        var serviceResponse = content.FromJsonOrDefault<ServiceResponse>();

        if (serviceResponse != null)
            message = serviceResponse.Message;

        return message;
    }
}