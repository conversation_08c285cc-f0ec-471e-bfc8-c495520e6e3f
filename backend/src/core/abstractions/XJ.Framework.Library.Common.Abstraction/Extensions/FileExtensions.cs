using System.Text;

namespace XJ.Framework.Library.Common.Abstraction.Extensions;

public static class FileExtensions
{
    public class FileStringContent
    {
        public string Content { get; set; }

        public FileStringContent(string content)
        {
            Content = content;
        }
    }

    public class FileBytesContent
    {
        public byte[] Content { get; set; }

        public FileBytesContent(byte[] content)
        {
            Content = content;
        }
    }

    public class FileNamePath
    {
        public string FilePath { get; set; }

        public FileNamePath(string filePath)
        {
            FilePath = filePath;
        }
    }

    public static string GetValidateFilePath(this FileNamePath fileNamePath)
    {
        var invalidChars = Path.GetInvalidFileNameChars();

        var result = fileNamePath.FilePath;

        invalidChars.ForEach(ch => { result = result.Replace(ch, '_'); });

        return result;
    }

    public static async Task<string> GetFileContentAsync(this FileNamePath fileNamePath,
        FileMode fileMode = FileMode.Open)
    {
        var stream = new FileStream(fileNamePath.FilePath, fileMode);
        return await stream.ReadToEndAsync();
    }

    public static async Task<IEnumerable<string>> GetFileLinesAsync(this FileNamePath fileNamePath)
    {
        return (await File.ReadAllLinesAsync(fileNamePath.FilePath)).ToList();
    }

    public static async Task<Stream> GetFileStreamAsync(this FileNamePath fileNamePath)
    {
        var stream = new FileStream(fileNamePath.FilePath, FileMode.Open);
        return await Task.FromResult(stream);
    }

    public static async Task<byte[]> GetFileBytesAsync(this FileNamePath fileNamePath)
    {
        return await File.ReadAllBytesAsync(fileNamePath.FilePath);
    }

    public static bool DeleteFileWhenExist(this FileNamePath fileNamePath)
    {
        if (!File.Exists(fileNamePath.FilePath))
        {
            return false;
        }

        File.Delete(fileNamePath.FilePath);
        return true;
    }

    public static async Task SaveFileAsync(this FileStringContent stringContent, string path)
    {
        var file = new FileInfo(path);

        await stringContent.SaveFileAsync(file.Directory!.FullName, file.Name);
    }

    public static async Task AppendFileAsync(this FileStringContent stringContent, string path)
    {
        var file = new FileInfo(path);

        await stringContent.AppendFileAsync(file.Directory!.FullName, file.Name);
    }

    public static async Task AppendFileAsync(this FileStringContent stringContent, string directory,
        string fileName)
    {
        if (!Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
        }

        await File.AppendAllTextAsync(Path.Combine(directory, fileName), stringContent.Content);
    }

    public static async Task SaveFileAsync(this FileStringContent stringContent, string directory,
        string fileName)
    {
        if (!Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
        }

        await File.WriteAllTextAsync(Path.Combine(directory, fileName), stringContent.Content);
    }

    public static async Task SaveFileAsync(this FileBytesContent bytesContent, string path)
    {
        var file = new FileInfo(path);
        await bytesContent.SaveFileAsync(file.Directory!.FullName, file.Name);
    }

    public static async Task SaveFileAsync(this FileBytesContent bytesContent, string directory,
        string fileName)
    {
        if (!Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
        }

        await File.WriteAllBytesAsync(Path.Combine(directory, fileName), bytesContent.Content);
    }

    public static async Task SaveFileAsync(this byte[] bytes, string destinationFileName)
    {
        ArgumentNullException.ThrowIfNull(bytes);
        ArgumentNullException.ThrowIfNull(destinationFileName);


        //var directoryPath = destinationFileName.Substring(0, destinationFileName.LastIndexOf('/'));
        var directoryPath = Path.GetDirectoryName(destinationFileName);

        if (!Directory.Exists(directoryPath))
            Directory.CreateDirectory(directoryPath!);

        await File.WriteAllBytesAsync(destinationFileName, bytes);
    }

    public static async Task<string> GetByteStringAsync(this byte[] bytes, Encoding? encoding = null)
    {
        return await Task.FromResult((encoding ?? Encoding.UTF8).GetString(bytes));
    }

    public static async Task<byte[]> ToBytesAsync(this FileInfo fileInfo)
    {
        await using var stream = fileInfo.Open(FileMode.Open);

        return await stream.ToBytesAsync();
    }
}