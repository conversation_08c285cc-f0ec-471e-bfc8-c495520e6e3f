using System.Text.Json;
using System.Text.Json.Nodes;

namespace XJ.Framework.Library.Common.Abstraction.Extensions;

public static class JsonNodeExtensions
{
    /// <summary>
    /// 枚举一个对象的第一级属性
    /// </summary>
    /// <param name="node"></param>
    /// <param name="action"></param>
    public static void EnumerateProperties(this JsonNode? node, Action<string, JsonNode?> action)
    {
        if (node != null && action != null)
        {
            if (node.GetValueKind() == JsonValueKind.Object)
            {
                var jsonObj = node.AsObject();

                jsonObj.AsEnumerable().ForEach(kv => action(kv.Key, kv.Value));
            }
        }
    }

    public static JsonNode? GetChildNode(this JsonNode? node, string path)
    {
        var result = node;

        if (path.IsNotEmpty())
        {
            string[] parts = path.Split('.');

            result = result.GetChildNode(parts);
        }

        return result;
    }

    public static T GetChildNodeValue<T>(this JsonNode? node, string path, T defaultValue)
    {
        var result = defaultValue;

        var childNode = node.GetChildNode(path);

        if (childNode != null)
            result = childNode.GetValue<T>();

        return result;
    }

    public static T GetChildNodeValue<T>(this JsonNode? node, string[] paths, T defaultValue)
    {
        var result = defaultValue;

        var childNode = node.GetChildNode(paths);

        if (childNode != null)
            result = childNode.GetValue<T>();

        return result;
    }

    public static JsonNode? GetChildNode(this JsonNode? node, string[] paths)
    {
        var result = node;

        if (result != null && paths != null)
        {
            for (var i = 0; i < paths.Length; i++)
            {
                result = result[paths[i]]!;

                if (result == null)
                    break;
            }
        }

        return result;
    }

    public static JsonArray? GetChildArray(this JsonNode? node, string[] paths)
    {
        return node.GetChildNode(paths) as JsonArray;
    }

    public static JsonArray? GetChildArray(this JsonNode? node, string path)
    {
        return node.GetChildNode(path) as JsonArray;
    }

    public static void ForEach(this JsonArray? array, Action<JsonNode> action)
    {
        if (array != null)
        {
            foreach (var node in array)
            {
                if (node != null)
                    action(node);
            }
        }
    }
}