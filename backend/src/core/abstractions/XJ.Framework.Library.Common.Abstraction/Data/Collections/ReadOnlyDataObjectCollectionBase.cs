using System.Collections;
using System.Diagnostics;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Data.Collections
{
    [DebuggerDisplay("Count = {Count}")]
    public abstract class ReadOnlyDataObjectCollectionBase<T> : CollectionBase, IEnumerable<T>
    {
        /// <summary>
        /// 构造方法
        /// </summary>
        protected ReadOnlyDataObjectCollectionBase()
        {
        }

        /// <summary>
        /// 构造方法。集合增加时的分配冗余
        /// </summary>
        /// <param name="capacity"></param>
        protected ReadOnlyDataObjectCollectionBase(int capacity)
            : base(capacity)
        {
        }

        /// <summary>
        /// 集合中数据添加后的事件
        /// </summary>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Design", "CA1009:DeclareEventHandlersCorrectly")]
        public event Action<ReadOnlyDataObjectCollectionBase<T>, int, T>? DataInserted;

        /// <summary>
        /// 迭代处理每一个元素
        /// </summary>
        /// <param name="action"></param>
        /// <returns>返回自己</returns>
        public virtual ReadOnlyDataObjectCollectionBase<T> ForEach(Action<T> action)
        {
            (action != null).FalseThrow<ArgumentNullException>(nameof(action));

            foreach (T item in List)
                action!(item);

            return this;
        }

        /// <summary>
        /// 判断集合中是否存在某元素
        /// </summary>
        /// <param name="match"></param>
        /// <returns></returns>
        public virtual bool Exists(Predicate<T> match)
        {
            (match != null).FalseThrow<ArgumentNullException>(nameof(match));

            var result = false;

            foreach (T item in List)
            {
                if (match!(item))
                {
                    result = true;
                    break;
                }
            }

            return result;
        }

        /// <summary>
        /// 判断集合中每个元素是否都满足某条件。如果集合为空，也返回True
        /// </summary>
        /// <param name="match"></param>
        /// <returns></returns>
        public virtual bool TrueForAll(Predicate<T> match)
        {
            (match != null).FalseThrow<ArgumentNullException>(nameof(match));

            var result = true;

            foreach (T item in List)
            {
                if (match!(item) == false)
                {
                    result = false;
                    break;
                }
            }

            return result;
        }

        /// <summary>
        /// 判断集合中每个元素是否都满足某条件，且集合不为空
        /// </summary>
        /// <param name="match"></param>
        /// <returns></returns>
        public virtual bool TrueForAllAndNotEmpty(Predicate<T> match)
        {
            (match != null).FalseThrow<ArgumentNullException>(nameof(match));

            var result = (this.Count > 0);

            if (result)
            {
                foreach (T item in List)
                {
                    if (match!(item) == false)
                    {
                        result = false;
                        break;
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 在集合中查找满足匹配条件的第一个元素
        /// </summary>
        /// <param name="match"></param>
        /// <returns></returns>
        public virtual T? Find(Predicate<T> match)
        {
            (match != null).FalseThrow<ArgumentNullException>(nameof(match));

            var result = default(T);

            foreach (T item in List)
            {
                if (match!(item))
                {
                    result = item;
                    break;
                }
            }

            return result;
        }

        /// <summary>
        /// 从后向前查找，找到第一个匹配的元素
        /// </summary>
        /// <param name="match"></param>
        /// <returns></returns>
        public virtual T? FindLast(Predicate<T> match)
        {
            (match != null).FalseThrow<ArgumentNullException>(nameof(match));

            var result = default(T);

            for (var i = this.Count - 1; i >= 0; i--)
            {
                if (match!(this.GetItemValue(i)))
                {
                    result = this.GetItemValue(i);
                    break;
                }
            }

            return result;
        }

        /// <summary>
        /// 找到满足匹配条件的所有元素
        /// </summary>
        /// <param name="match"></param>
        /// <returns></returns>
        public virtual IList<T> FindAll(Predicate<T> match)
        {
            IList<T> result = new List<T>();

            foreach (T item in List)
            {
                if (match(item))
                    result.Add(item);
            }

            return result;
        }

        /// <summary>
        /// 是否包含某个元素
        /// </summary>
        /// <param name="item"></param>
        /// <returns></returns>
        public virtual bool Contains(T item)
        {
            (item != null).FalseThrow<ArgumentNullException>(nameof(item));

            return List.Contains(item);
        }

        /// <summary>
        /// 得到某个元素的位置
        /// </summary>
        /// <param name="item"></param>
        /// <returns></returns>
        public virtual int IndexOf(T item)
        {
            (item != null).FalseThrow<ArgumentNullException>(nameof(item));

            return List.IndexOf(item);
        }

        public virtual int IndexOf(Predicate<T> predicate)
        {
            predicate.NullCheck();

            var result = -1;

            for (var i = 0; i < List.Count; i++)
            {
                if (predicate((T)List[i]!))
                {
                    result = i;
                    break;
                }
            }

            return result;
        }

        /// <summary>
        /// 复制到别的集合中
        /// </summary>
        /// <param name="collection"></param>
        public void CopyTo(ICollection<T> collection)
        {
            this.CopyTo(collection, null, null);
        }

        /// <summary>
        /// 复制到别的集合中。带筛选条件
        /// </summary>
        /// <param name="collection">集合</param>
        /// <param name="predicate">筛选条件</param>
        public void CopyTo(ICollection<T> collection, Predicate<T>? predicate)
        {
            this.CopyTo(collection, predicate, null);
        }

        /// <summary>
        /// 复制到别的集合中。带筛选条件
        /// </summary>
        /// <param name="collection">集合</param>
        /// <param name="converter">转换器</param>
        public void CopyTo(ICollection<T> collection, Converter<T, T> converter)
        {
            this.CopyTo(collection, null, converter);
        }

        /// <summary>
        /// 复制到别的集合中。过程中可以带筛选条件和转换器
        /// </summary>
        /// <param name="collection"></param>
        /// <param name="predicate">筛选条件</param>
        /// <param name="converter">转换器</param>
        public virtual void CopyTo(ICollection<T> collection, Predicate<T>? predicate, Converter<T, T>? converter)
        {
            (collection != null).FalseThrow<ArgumentNullException>(nameof(collection));

            this.ForEach(delegate(T item)
            {
                if (predicate == null || predicate(item))
                {
                    var newItem = item;

                    if (converter != null)
                        newItem = converter(item);

                    collection!.Add(newItem);
                }
            });
        }

        /// <summary>
        /// 转换到数组
        /// </summary>
        /// <returns></returns>
        public virtual T[] ToArray()
        {
            T[] result = new T[this.Count];

            for (var i = 0; i < this.Count; i++)
                result[i] = this.GetItemValue(i);

            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="obj"></param>
        protected virtual void InnerAdd(T obj)
        {
            (obj != null).FalseThrow<ArgumentNullException>(nameof(obj));

            List.Add(obj);
        }

        /// <summary>
        /// 内部插入
        /// </summary>
        /// <param name="index"></param>
        /// <param name="obj"></param>
        protected virtual void InnerInsert(int index, T obj)
        {
            (obj != null).FalseThrow<ArgumentNullException>(nameof(obj));

            List.Insert(index, obj);
        }

        #region IEnumerable<T> 成员

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public new IEnumerator<T> GetEnumerator()
        {
            foreach (T item in List)
                yield return item;
        }

        #endregion

        /// <summary>
        /// 当数据添加后
        /// </summary>
        /// <param name="index"></param>
        /// <param name="value"></param>
        protected override void OnInsertComplete(int index, object? value)
        {
            base.OnInsertComplete(index, value);

            if (DataInserted != null)
                DataInserted(this, index, (T)value!);
        }

        protected T GetItemValue(int index)
        {
            return (T)this.List[index]!;
        }
    }
}