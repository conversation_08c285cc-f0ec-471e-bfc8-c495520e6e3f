using System.Reflection;
using XJ.Framework.Library.Common.Abstraction.Converters;
using XJ.Framework.Library.Common.Abstraction.Data.Collections;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Expressions;

/// <summary>
/// 内置函数说明
/// </summary>
public class BuiltInFunctionInfo
{
    /// <summary>
    /// 构造方法
    /// </summary>
    internal BuiltInFunctionInfo(BuiltInFunctionAttribute attribute, MethodInfo methodInfo)
    {
        attribute.NullCheck("attribute");
        methodInfo.NullCheck("methodInfo");

        this.FunctionName = attribute.FunctionName;
        this.Description = attribute.Description;
        this.MethodInfo = methodInfo;
    }

    /// <summary>
    /// 函数名称
    /// </summary>
    public string FunctionName {
        get;
        private set;
    }

    /// <summary>
    /// 函数说明
    /// </summary>
    public string Description {
        get;
        private set;
    }

    /// <summary>
    /// 方法的反射信息
    /// </summary>
    public MethodInfo MethodInfo {
        get;
        private set;
    }

    /// <summary>
    /// 执行方法
    /// </summary>
    /// <param name="target"></param>
    /// <param name="arrParams"></param>
    /// <param name="callerContext"></param>
    /// <returns></returns>
    public object? ExecuteFunction(object? target, ParamObjectCollection arrParams, object? callerContext)
    {
        ParameterInfo[] methodParams = this.MethodInfo.GetParameters();

        CheckParametersCount(this.MethodInfo.Name, methodParams, arrParams);

        return this.MethodInfo.Invoke(target, PrepareParameters(methodParams, arrParams, callerContext));
    }

    private object?[] PrepareParameters(IList<ParameterInfo> methodParams, ParamObjectCollection arrParams,
        object? callerContext)
    {
        object?[] result = new object[methodParams.Count];

        for (var i = 0; i < arrParams.Count; i++)
        {
            var pi = methodParams[i];

            try
            {
                result[i] = DataConverter.ChangeType(arrParams[i].Value, pi.ParameterType);
            }
            catch (System.Exception ex)
            {
                throw new InvalidCastException(string.Format("内置函数{0}参数{1}类型转换错误。{2}",
                    this.FunctionName, pi.Name, ex.Message), ex);
            }
        }

        //如果方法的参数比表达式的参数多一个，则将调用上下文作为最后一个参数
        if (result.Length == arrParams.Count + 1)
            result[arrParams.Count] = callerContext;

        return result;
    }

    private static void CheckParametersCount(string methodName, ParameterInfo[] methodParams,
        ParamObjectCollection arrParams)
    {
        (methodParams.Length == arrParams.Count || methodParams.Length == arrParams.Count + 1)
            .FalseThrow<ArgumentOutOfRangeException>(
                "方法{0}内置的表达式方法的参数是{1}个，而调用参数是{2}个，不匹配",
                methodName,
                methodParams.Length,
                arrParams.Count);
    }
}

/// <summary>
/// 内置的方法信息集合
/// </summary>
public class BuiltInFunctionInfoCollection : EditableKeyedDataObjectCollectionBase<string, BuiltInFunctionInfo>
{
    /// <summary>
    /// 构造方法
    /// </summary>
    public BuiltInFunctionInfoCollection()
        : base(StringComparer.OrdinalIgnoreCase)
    {
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="item"></param>
    /// <returns></returns>
    protected override string GetKeyForItem(BuiltInFunctionInfo item)
    {
        return item.FunctionName;
    }
}