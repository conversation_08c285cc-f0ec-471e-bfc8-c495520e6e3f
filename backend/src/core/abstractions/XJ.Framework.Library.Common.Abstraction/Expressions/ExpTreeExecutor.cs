using XJ.Framework.Library.Common.Abstraction.Converters;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Expressions;

/// <summary>
/// 语法分析Tree的计算类
/// </summary>
internal partial class ExpTreeExecutor
{
    public static readonly ExpTreeExecutor Instance = new ExpTreeExecutor();

    private ExpTreeExecutor()
    {
    }

    /// <summary>
    /// 获取树结点值
    /// </summary>
    /// <param name="tree">二叉树节点</param>
    /// <param name="options">包含自定义函数的计算参数</param>
    /// <returns>树结点值</returns>
    public static object? GetValue(ExpTreeNode? tree, UserFunctionCalculationOptions options)
    {
        options.NullCheck();

        object? result = null;

        if (tree != null)
        {
            var calcContext = new CalculateContext
            {
                Optimize = options.Optimize,
                CallerContext = options.CallerContext,
                CalculateUserFunction = options.UserFunction,
                ExpressionDictionaries = options.ExpressionDictionaries
            };

            result = VExp(tree, calcContext);
        }

        return result;
    }

    /// <summary>
    /// 获取树结点值
    /// </summary>
    /// <param name="tree">二叉树节点</param>
    /// <param name="options">包含内置函数的计算参数</param>
    /// <returns>树结点值</returns>
    public static object? GetValue(ExpTreeNode? tree, BuiltInFunctionsCalculationOptions options)
    {
        object? result = null;

        if (tree != null)
        {
            var calcContext = new CalculateContext
            {
                Optimize = options.Optimize,
                CallerContext = options.CallerContext,
                BuiltInFunctionsWrapper = options.BuiltInFunctionsWrapper,
                ExpressionDictionaries = options.ExpressionDictionaries
            };

            result = VExp(tree, calcContext);
        }

        return result;
    }

    private static object? VExp(ExpTreeNode? node, CalculateContext calcContext)
    {
        object? oValue = null;

        if (node != null)
        {
            try
            {
                switch (node.OperationID)
                {
                    case Operation_IDs.OI_NUMBER:
                    case Operation_IDs.OI_STRING:
                    case Operation_IDs.OI_NEG:
                    case Operation_IDs.OI_BOOLEAN:
                    case Operation_IDs.OI_DATETIME:
                        oValue = node.Value;
                        break;
                    case Operation_IDs.OI_ADD:
                        oValue = ExecuteFilter(VExp(node.Left, calcContext), VExp(node.Right, calcContext), AddBuiltIn,
                            node.Position, calcContext);
                        break;
                    case Operation_IDs.OI_MINUS:
                        oValue = ExecuteFilter(VExp(node.Left, calcContext), VExp(node.Right, calcContext),
                            MinusBuiltIn, node.Position, calcContext);
                        break;
                    case Operation_IDs.OI_MUL:
                        oValue = ExecuteFilter(VExp(node.Left, calcContext), VExp(node.Right, calcContext), MulBuiltIn,
                            node.Position, calcContext);
                        break;
                    case Operation_IDs.OI_DIV:
                        oValue = ExecuteFilter(VExp(node.Left, calcContext), VExp(node.Right, calcContext), DivBuiltIn,
                            node.Position, calcContext);
                        break;
                    case Operation_IDs.OI_LOGICAL_OR:
                    {
                        oValue = (bool?)VExp(node.Left, calcContext);
                        object? oRight = (bool)false;

                        if ((bool?)oValue == false)
                            oRight = VExp(node.Right, calcContext);

                        CheckOperandNull(oValue, oRight, node.Position);
                        oValue = (bool)oValue! || (bool)oRight!;
                    }
                        break;
                    case Operation_IDs.OI_LOGICAL_AND:
                    {
                        oValue = (bool?)VExp(node.Left, calcContext);
                        object? oRight = (bool)true;

                        if ((bool?)oValue == true)
                            oRight = VExp(node.Right, calcContext);

                        CheckOperandNull(oValue, oRight, node.Position);
                        oValue = (bool)oValue! && (bool)oRight!;
                    }
                        break;
                    case Operation_IDs.OI_NOT:
                        oValue = VExp(node.Right, calcContext);
                        CheckOperandNull(oValue, node.Position);
                        oValue = !(bool)oValue!;
                        break;
                    case Operation_IDs.OI_GREAT:
                        oValue = CompareGreatOP(VExp(node.Left, calcContext), VExp(node.Right, calcContext),
                            node.Position);
                        break;
                    case Operation_IDs.OI_GREATEQUAL:
                        oValue = CompareGreatEqualOP(VExp(node.Left, calcContext), VExp(node.Right, calcContext),
                            node.Position);
                        break;
                    case Operation_IDs.OI_LESS:
                        oValue = CompareLessOP(VExp(node.Left, calcContext), VExp(node.Right, calcContext),
                            node.Position);
                        break;
                    case Operation_IDs.OI_LESSEQUAL:
                        oValue = CompareLessEqualOP(VExp(node.Left, calcContext), VExp(node.Right, calcContext),
                            node.Position);
                        break;
                    case Operation_IDs.OI_NOT_EQUAL:
                        oValue = CompareNotEqualOP(VExp(node.Left, calcContext), VExp(node.Right, calcContext),
                            node.Position);
                        break;
                    case Operation_IDs.OI_EQUAL:
                        oValue = CompareEqualOP(VExp(node.Left, calcContext), VExp(node.Right, calcContext),
                            node.Position);
                        break;
                    case Operation_IDs.OI_USERDEFINE:
                        oValue = DoBuiltInFunctions(node, node.Params, calcContext);
                        break;
                    default:
                        throw ParsingException.NewParsingException(
                            ParseError.peInvalidOperator,
                            node.Position,
                            node.OperationID.GetShortName());
                }
            }
            catch (System.InvalidCastException ex)
            {
                throw ParsingException.NewParsingException(ParseError.peTypeMismatch, node.Position, ex);
            }
        }

        return oValue;
    }

    /// <summary>
    /// 计算表达式字典
    /// </summary>
    /// <param name="strFuncName"></param>
    /// <param name="arrParams"></param>
    /// <param name="calcContext"></param>
    /// <returns></returns>
    private static object? CalculateExpressionDictionary(string strFuncName, ParamObjectCollection arrParams,
        CalculateContext calcContext)
    {
        var dictionary = calcContext.ExpressionDictionaries[strFuncName];

        object? oValue = null;

        if (dictionary != null)
        {
            ExpressionDictionaryCalculatorContext context = new(dictionary, calcContext.CallerContext);

            var key = string.Empty;

            if (arrParams.Count > 0 && arrParams[0].Value != null)
                key = arrParams[0].Value!.ToString()!;

            oValue = dictionary.Calculator!.Calculate(strFuncName, key, context);
        }

        return oValue;
    }

    private static ParamObjectCollection GetParams(List<ExpTreeNode> arrParams, CalculateContext calcContext)
    {
        List<ParamObject> list = new List<ParamObject>();

        for (var i = 0; i < arrParams.Count; i++)
        {
            var node = (ExpTreeNode)arrParams[i];

            list.Add(new ParamObject(VExp(node, calcContext), node.Position, i));
        }

        return new ParamObjectCollection(list);
    }

    private static void CheckOperandNull(object? p, int nPos)
    {
        if (p == null)
            throw ParsingException.NewParsingException(ParseError.peNeedOperand, nPos);
    }

    private static void CheckOperandNull(object? p1, object? p2, int nPos)
    {
        if (p1 == null || p2 == null)
            throw ParsingException.NewParsingException(ParseError.peNeedOperand, nPos);
    }

    private static object CompareGreatOP(object? p1, object? p2, int nPos)
    {
        CheckOperandNull(p1, p2, nPos);
        bool result;

        if (p1 is System.DateTime || p2 is System.DateTime)
            result = (DateTime)DataConverter.ChangeType(p1, typeof(DateTime))! >
                     (DateTime)DataConverter.ChangeType(p2, typeof(DateTime))!;
        else
        {
            if (p1 is System.String || p2 is System.String)
                result = p1!.ToString()!.CompareTo(p2!.ToString()) > 0;
            else
                result = NToD(p1!) > NToD(p2!);
        }

        return result;
    }

    private static object CompareLessOP(object? p1, object? p2, int nPos)
    {
        CheckOperandNull(p1, p2, nPos);
        bool result;

        if (p1 is System.DateTime || p2 is System.DateTime)
            result = (DateTime)DataConverter.ChangeType(p1, typeof(DateTime))! <
                     (DateTime)DataConverter.ChangeType(p2, typeof(DateTime))!;
        else
        {
            if (p1 is System.String || p2 is System.String)
                result = p1!.ToString()!.CompareTo(p2!.ToString()) < 0;
            else
                result = NToD(p1!) < NToD(p2!);
        }

        return result;
    }

    private static object ContainsOP(object container, object content, bool ignoreCase)
    {
        var result = false;

        var comparisonType = StringComparison.Ordinal;

        if (ignoreCase)
            comparisonType = StringComparison.OrdinalIgnoreCase;

        result = container.ToString()!.IndexOf(content.ToString()!, 0, comparisonType) >= 0;

        return result;
    }

    private static object CompareEqualOP(object? p1, object? p2, int nPos)
    {
        CheckOperandNull(p1, p2, nPos);
        bool result;

        if (p1 is System.DateTime || p2 is System.DateTime)
            result = (DateTime)DataConverter.ChangeType(p1, typeof(DateTime))! ==
                     (DateTime)DataConverter.ChangeType(p2, typeof(DateTime))!;
        else if (p1 is System.String || p2 is System.String)
            result = p1!.ToString() == p2!.ToString();
        else
            result = NToD(p1!) == NToD(p2!);

        return result;
    }

    private static object CompareNotEqualOP(object? p1, object? p2, int nPos)
    {
        CheckOperandNull(p1, p2, nPos);
        bool result;

        if (p1 is System.DateTime || p2 is System.DateTime)
            result = (DateTime)DataConverter.ChangeType(p1, typeof(DateTime))! !=
                     (DateTime)DataConverter.ChangeType(p2, typeof(DateTime))!;
        else if (p1 is System.String || p2 is System.String)
            result = p1!.ToString() != p2!.ToString();
        else
            result = NToD(p1!) != NToD(p2!);

        return result;
    }

    private static object CompareGreatEqualOP(object? p1, object? p2, int nPos)
    {
        CheckOperandNull(p1, p2, nPos);
        bool result;

        if (p1 is System.DateTime || p2 is System.DateTime)
            result = (DateTime)DataConverter.ChangeType(p1, typeof(DateTime))! >=
                     (DateTime)DataConverter.ChangeType(p2, typeof(DateTime))!;
        else if (p1 is System.String || p2 is System.String)
            result = p1!.ToString()!.CompareTo(p2!.ToString()) >= 0;
        else
            result = NToD(p1!) >= NToD(p2!);

        return result;
    }

    private static object CompareLessEqualOP(object? p1, object? p2, int nPos)
    {
        CheckOperandNull(p1, p2, nPos);
        bool result;

        if (p1 is System.DateTime || p2 is System.DateTime)
            result = (DateTime)DataConverter.ChangeType(p1, typeof(DateTime))! <=
                     (DateTime)DataConverter.ChangeType(p2, typeof(DateTime))!;
        else if (p1 is System.String || p2 is System.String)
            result = p1!.ToString()!.CompareTo(p2!.ToString()) <= 0;
        else
            result = NToD(p1!) <= NToD(p2!);

        return result;
    }

    /// <summary>
    /// 将数字转换为Decimal
    /// </summary>
    /// <param name="p"></param>
    /// <returns></returns>
    private static decimal NToD(object? p)
    {
        return Convert.ToDecimal(p ?? 0M);
    }
}