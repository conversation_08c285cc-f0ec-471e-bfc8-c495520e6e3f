using System.Globalization;

namespace XJ.Framework.Library.Common.Abstraction.Models;

public class VersionTagFields : IComparable
{
    public static VersionTagFields Parse(string versionTag)
    {
        return new VersionTagFields(DateTimeOffset.Parse(versionTag, CultureInfo.InvariantCulture,
            DateTimeStyles.AssumeUniversal));
    }

    public string ToVersionTag()
    {
        return DateTimeOffset.ToString("yyyy-MM-dd HH:mm:ss.fff");
    }


    public readonly DateTimeOffset DateTimeOffset;

    public VersionTagFields(DateTimeOffset dateTimeOffset)
    {
        DateTimeOffset = dateTimeOffset;
    }

    public VersionTagFields(DateTime dateTime)
    {
        DateTimeOffset = dateTime;
    }


    public int CompareTo(object? obj)
    {
        return obj is not VersionTagFields other ? 1 : DateTimeOffset.CompareTo(other.DateTimeOffset);
    }
}