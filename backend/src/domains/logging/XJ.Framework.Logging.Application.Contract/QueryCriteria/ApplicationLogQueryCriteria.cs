
namespace XJ.Framework.Logging.Application.Contract.QueryCriteria;

/// <summary>
/// ApplicationLog 查询条件
/// </summary>
public class ApplicationLogQueryCriteria : BaseQueryCriteria
{
    [Equal] public string? Level { get; set; }

    [Contains] public string? Application { get; set; }

    [Equal] public string? Category { get; set; }

    [Contains] public string? Controller { get; set; }

    [Contains] public string? Action { get; set; }

    [Contains] public string? RouteTemplate { get; set; }

    [Equal] public string? CurrentUser { get; set; }

    [Equal] public string? CorrelationId { get; set; }

    [Contains] public string? Message { get; set; }

    [Contains] public string? ClientIp { get; set; }

    [Between] public List<DateTimeOffset> Timestamp { get; set; } = new();
}
