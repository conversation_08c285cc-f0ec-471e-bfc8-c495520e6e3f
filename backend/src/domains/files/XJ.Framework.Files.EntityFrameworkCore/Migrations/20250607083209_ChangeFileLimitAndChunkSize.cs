using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace XJ.Framework.Files.EntityFrameworkCore.Migrations
{
    /// <inheritdoc />
    public partial class ChangeFileLimitAndChunkSize : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                schema: "f",
                table: "file_types",
                keyColumn: "id",
                keyValue: 1923634605770194945L);

            migrationBuilder.DeleteData(
                schema: "f",
                table: "file_types",
                keyColumn: "id",
                keyValue: 1923634605770194946L);

            migrationBuilder.DeleteData(
                schema: "f",
                table: "file_types",
                keyColumn: "id",
                keyValue: 1923634605770194947L);

            migrationBuilder.DeleteData(
                schema: "f",
                table: "file_types",
                keyColumn: "id",
                keyValue: 1923634605770194948L);

            migrationBuilder.DeleteData(
                schema: "f",
                table: "file_types",
                keyColumn: "id",
                keyValue: 1923634605770194949L);

            migrationBuilder.DeleteData(
                schema: "f",
                table: "file_types",
                keyColumn: "id",
                keyValue: 1923634605770194950L);

            migrationBuilder.DeleteData(
                schema: "f",
                table: "storages",
                keyColumn: "id",
                keyValue: 1923634605770194944L);

            migrationBuilder.InsertData(
                schema: "f",
                table: "file_types",
                columns: new[] { "id", "chunk_limit", "created_by", "created_time", "is_deleted", "extensions", "last_modified_by", "last_modified_time", "PermissionRequired", "remark", "size_limit", "type_code", "type_name" },
                values: new object[,]
                {
                    { 1931267926531297281L, 5242880L, "system", new DateTimeOffset(new DateTime(2025, 6, 7, 8, 32, 9, 565, DateTimeKind.Unspecified).AddTicks(7190), new TimeSpan(0, 0, 0, 0, 0)), false, ".jpg,.jpeg,.png", "system", new DateTimeOffset(new DateTime(2025, 6, 7, 8, 32, 9, 565, DateTimeKind.Unspecified).AddTicks(7190), new TimeSpan(0, 0, 0, 0, 0)), "login", "头像文件类型", 5242880L, "avatar", "头像" },
                    { 1931267926531297282L, 5242880L, "system", new DateTimeOffset(new DateTime(2025, 6, 7, 8, 32, 9, 565, DateTimeKind.Unspecified).AddTicks(7190), new TimeSpan(0, 0, 0, 0, 0)), false, ".pdf", "system", new DateTimeOffset(new DateTime(2025, 6, 7, 8, 32, 9, 565, DateTimeKind.Unspecified).AddTicks(7190), new TimeSpan(0, 0, 0, 0, 0)), "login", "", 5242880L, "ethic_committee_approved_file", "伦理委员会审批件" },
                    { 1931267926531297283L, 5242880L, "system", new DateTimeOffset(new DateTime(2025, 6, 7, 8, 32, 9, 565, DateTimeKind.Unspecified).AddTicks(7190), new TimeSpan(0, 0, 0, 0, 0)), false, ".pdf", "system", new DateTimeOffset(new DateTime(2025, 6, 7, 8, 32, 9, 565, DateTimeKind.Unspecified).AddTicks(7190), new TimeSpan(0, 0, 0, 0, 0)), "login", "", 5242880L, "mpa_approved_file", "国家药监局批准附件" },
                    { 1931267926531297284L, 5242880L, "system", new DateTimeOffset(new DateTime(2025, 6, 7, 8, 32, 9, 565, DateTimeKind.Unspecified).AddTicks(7190), new TimeSpan(0, 0, 0, 0, 0)), false, ".pdf", "system", new DateTimeOffset(new DateTime(2025, 6, 7, 8, 32, 9, 565, DateTimeKind.Unspecified).AddTicks(7190), new TimeSpan(0, 0, 0, 0, 0)), "login", "", 5242880L, "study_protocol", "研究方案" },
                    { 1931267926531297285L, 5242880L, "system", new DateTimeOffset(new DateTime(2025, 6, 7, 8, 32, 9, 565, DateTimeKind.Unspecified).AddTicks(7190), new TimeSpan(0, 0, 0, 0, 0)), false, ".pdf", "system", new DateTimeOffset(new DateTime(2025, 6, 7, 8, 32, 9, 565, DateTimeKind.Unspecified).AddTicks(7190), new TimeSpan(0, 0, 0, 0, 0)), "login", "", 5242880L, "informed_consent_file", "知情同意书" },
                    { 1931267926531297286L, 5242880L, "system", new DateTimeOffset(new DateTime(2025, 6, 7, 8, 32, 9, 565, DateTimeKind.Unspecified).AddTicks(7190), new TimeSpan(0, 0, 0, 0, 0)), false, ".pdf", "system", new DateTimeOffset(new DateTime(2025, 6, 7, 8, 32, 9, 565, DateTimeKind.Unspecified).AddTicks(7190), new TimeSpan(0, 0, 0, 0, 0)), "login", "", 5242880L, "statistical_results_file", "上传试验完成后的统计结果" }
                });

            migrationBuilder.InsertData(
                schema: "f",
                table: "storages",
                columns: new[] { "id", "access_key", "bucket", "created_by", "created_time", "is_deleted", "endpoint", "last_modified_by", "last_modified_time", "protocol", "remark", "secret_key", "storage_code", "storage_name" },
                values: new object[] { 1931267926531297280L, "", "default", "system", new DateTimeOffset(new DateTime(2025, 6, 7, 8, 32, 9, 565, DateTimeKind.Unspecified).AddTicks(7190), new TimeSpan(0, 0, 0, 0, 0)), false, "", "system", new DateTimeOffset(new DateTime(2025, 6, 7, 8, 32, 9, 565, DateTimeKind.Unspecified).AddTicks(7190), new TimeSpan(0, 0, 0, 0, 0)), 1, null, "", "default", "默认存储" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                schema: "f",
                table: "file_types",
                keyColumn: "id",
                keyValue: 1931267926531297281L);

            migrationBuilder.DeleteData(
                schema: "f",
                table: "file_types",
                keyColumn: "id",
                keyValue: 1931267926531297282L);

            migrationBuilder.DeleteData(
                schema: "f",
                table: "file_types",
                keyColumn: "id",
                keyValue: 1931267926531297283L);

            migrationBuilder.DeleteData(
                schema: "f",
                table: "file_types",
                keyColumn: "id",
                keyValue: 1931267926531297284L);

            migrationBuilder.DeleteData(
                schema: "f",
                table: "file_types",
                keyColumn: "id",
                keyValue: 1931267926531297285L);

            migrationBuilder.DeleteData(
                schema: "f",
                table: "file_types",
                keyColumn: "id",
                keyValue: 1931267926531297286L);

            migrationBuilder.DeleteData(
                schema: "f",
                table: "storages",
                keyColumn: "id",
                keyValue: 1931267926531297280L);

            migrationBuilder.InsertData(
                schema: "f",
                table: "file_types",
                columns: new[] { "id", "chunk_limit", "created_by", "created_time", "is_deleted", "extensions", "last_modified_by", "last_modified_time", "PermissionRequired", "remark", "size_limit", "type_code", "type_name" },
                values: new object[,]
                {
                    { 1923634605770194945L, 8388608L, "system", new DateTimeOffset(new DateTime(2025, 5, 17, 7, 0, 4, 75, DateTimeKind.Unspecified).AddTicks(2730), new TimeSpan(0, 0, 0, 0, 0)), false, ".jpg,.jpeg,.png", "system", new DateTimeOffset(new DateTime(2025, 5, 17, 7, 0, 4, 75, DateTimeKind.Unspecified).AddTicks(2730), new TimeSpan(0, 0, 0, 0, 0)), "login", "头像文件类型", 5242880L, "avatar", "头像" },
                    { 1923634605770194946L, 8388608L, "system", new DateTimeOffset(new DateTime(2025, 5, 17, 7, 0, 4, 75, DateTimeKind.Unspecified).AddTicks(2730), new TimeSpan(0, 0, 0, 0, 0)), false, ".pdf", "system", new DateTimeOffset(new DateTime(2025, 5, 17, 7, 0, 4, 75, DateTimeKind.Unspecified).AddTicks(2730), new TimeSpan(0, 0, 0, 0, 0)), "login", "", 52428800L, "ethic_committee_approved_file", "伦理委员会审批件" },
                    { 1923634605770194947L, 8388608L, "system", new DateTimeOffset(new DateTime(2025, 5, 17, 7, 0, 4, 75, DateTimeKind.Unspecified).AddTicks(2730), new TimeSpan(0, 0, 0, 0, 0)), false, ".pdf", "system", new DateTimeOffset(new DateTime(2025, 5, 17, 7, 0, 4, 75, DateTimeKind.Unspecified).AddTicks(2730), new TimeSpan(0, 0, 0, 0, 0)), "login", "", 52428800L, "mpa_approved_file", "国家药监局批准附件" },
                    { 1923634605770194948L, 8388608L, "system", new DateTimeOffset(new DateTime(2025, 5, 17, 7, 0, 4, 75, DateTimeKind.Unspecified).AddTicks(2730), new TimeSpan(0, 0, 0, 0, 0)), false, ".pdf", "system", new DateTimeOffset(new DateTime(2025, 5, 17, 7, 0, 4, 75, DateTimeKind.Unspecified).AddTicks(2730), new TimeSpan(0, 0, 0, 0, 0)), "login", "", 52428800L, "study_protocol", "研究方案" },
                    { 1923634605770194949L, 8388608L, "system", new DateTimeOffset(new DateTime(2025, 5, 17, 7, 0, 4, 75, DateTimeKind.Unspecified).AddTicks(2730), new TimeSpan(0, 0, 0, 0, 0)), false, ".pdf", "system", new DateTimeOffset(new DateTime(2025, 5, 17, 7, 0, 4, 75, DateTimeKind.Unspecified).AddTicks(2730), new TimeSpan(0, 0, 0, 0, 0)), "login", "", 52428800L, "informed_consent_file", "知情同意书" },
                    { 1923634605770194950L, 8388608L, "system", new DateTimeOffset(new DateTime(2025, 5, 17, 7, 0, 4, 75, DateTimeKind.Unspecified).AddTicks(2730), new TimeSpan(0, 0, 0, 0, 0)), false, ".pdf", "system", new DateTimeOffset(new DateTime(2025, 5, 17, 7, 0, 4, 75, DateTimeKind.Unspecified).AddTicks(2730), new TimeSpan(0, 0, 0, 0, 0)), "login", "", 52428800L, "statistical_results_file", "上传试验完成后的统计结果" }
                });

            migrationBuilder.InsertData(
                schema: "f",
                table: "storages",
                columns: new[] { "id", "access_key", "bucket", "created_by", "created_time", "is_deleted", "endpoint", "last_modified_by", "last_modified_time", "protocol", "remark", "secret_key", "storage_code", "storage_name" },
                values: new object[] { 1923634605770194944L, "", "default", "system", new DateTimeOffset(new DateTime(2025, 5, 17, 7, 0, 4, 75, DateTimeKind.Unspecified).AddTicks(2730), new TimeSpan(0, 0, 0, 0, 0)), false, "", "system", new DateTimeOffset(new DateTime(2025, 5, 17, 7, 0, 4, 75, DateTimeKind.Unspecified).AddTicks(2730), new TimeSpan(0, 0, 0, 0, 0)), 1, null, "", "default", "默认存储" });
        }
    }
}
