

// global using 指令

// System 命名空间
global using Microsoft.Extensions.DependencyInjection;

// Microsoft 命名空间
global using Microsoft.EntityFrameworkCore;
global using Microsoft.Extensions.Configuration;

// XJ.Framework 基础库
global using XJ.Framework.Library.EntityFrameworkCore;
global using XJ.Framework.Library.EntityFrameworkCore.Contexts;

global using Microsoft.AspNetCore.Hosting;
global using Microsoft.Extensions.Hosting;
global using Microsoft.Extensions.Options;
global using XJ.Framework.Library.Domain.UOW;
global using XJ.Framework.Library.EntityFrameworkCore.Extensions;
global using XJ.Framework.Library.EntityFrameworkCore.UOW;
global using XJ.Framework.Library.EntityFrameworkCore.Repositories;
global using XJ.Framework.Library.Infrastructure.ApiClient.Options;
global using XJ.Framework.Files.Domain.Entities; 
global using XJ.Framework.Files.Domain.Repositories.Interfaces;


