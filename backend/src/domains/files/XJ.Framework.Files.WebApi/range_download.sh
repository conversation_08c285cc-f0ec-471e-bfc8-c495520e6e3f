#!/bin/bash

# 用法: ./range_download.sh 文件ID [JWT_TOKEN] [DEVICE_ID] [BASE_URL]
# BASE_URL默认为 http://localhost:4095/FileInfo

FILE_ID="$1"
FILE_NAME="$2"
JWT_TOKEN="$3"
DEVICE_ID="$4"
BASE_URL="${5:-http://localhost:4095/FileInfo}"
DOWNLOAD_URL="$BASE_URL/$FILE_ID/$FILE_NAME"

OUT_FILE="downloaded_file.bin"
CHUNK_SIZE=$((1024 * 1024)) # 1MB

# 检查依赖
command -v curl >/dev/null 2>&1 || {
    echo >&2 "curl 未安装"
    exit 1
}

# 构造header参数数组
CURL_HEADERS=()
if [ -n "$JWT_TOKEN" ]; then
  CURL_HEADERS+=(-H "Authorization: Bearer $JWT_TOKEN")
fi
if [ -n "$DEVICE_ID" ]; then
  CURL_HEADERS+=(-H "x-device-id: $DEVICE_ID")
fi

echo "[调试] 请求文件头信息: curl -sI ${CURL_HEADERS[@]} $DOWNLOAD_URL"
HEADERS_OUTPUT=$(curl -s -X GET -I "${CURL_HEADERS[@]}" "$DOWNLOAD_URL")
echo "[调试] 响应头内容:\n$HEADERS_OUTPUT"

content_range=$(echo "$HEADERS_OUTPUT" | grep -i "Content-Range")
echo "[调试] Content-Range grep结果: $content_range"

total_size=""
if [[ -n "$content_range" ]]; then
    total_size=$(echo "$content_range" | awk -F'/' '{print $2}' | tr -d '\r')
    echo "[调试] Content-Range解析total_size: $total_size"
else
    content_length=$(echo "$HEADERS_OUTPUT" | grep -i "Content-Length" | head -1 | tr -d '\r')
    echo "[调试] Content-Length grep结果: $content_length"
    total_size=$(echo "$content_length" | awk '{print $2}')
    echo "[调试] Content-Length解析total_size: $total_size"
fi

if [ -z "$total_size" ]; then
  echo "无法获取文件大小"
  exit 1
fi

echo "文件总大小: $total_size 字节"

echo "[调试] 开始分块下载..."
# 分块下载
start=0
> "$OUT_FILE"
while [ $start -lt $total_size ]; do
  end=$((start + CHUNK_SIZE - 1))
  if [ $end -ge $total_size ]; then
    end=$((total_size - 1))
  fi
  echo "下载字节: $start-$end"
  echo "[调试] curl -s ${CURL_HEADERS[@]} -H 'Range: bytes=${start}-${end}' $DOWNLOAD_URL >> $OUT_FILE"
  http_code=$(curl -s -w "%{http_code}" -o temp_chunk.bin "${CURL_HEADERS[@]}" -H "Range: bytes=${start}-${end}" "$DOWNLOAD_URL")
  cat temp_chunk.bin >> "$OUT_FILE"
  rm -f temp_chunk.bin
  echo "[调试] 本次分块返回状态码: $http_code"
  start=$((end + 1))
done

echo "分块下载完成，文件已保存为 $OUT_FILE"

echo "[调试] 开始断点续传测试..."
# 断点续传测试
PARTIAL_FILE="partial_file.bin"
HALF=$((total_size / 2))
echo "先下载前半部分: 0-$(($HALF - 1))"
echo "[调试] curl -s ${CURL_HEADERS[@]} -H 'Range: bytes=0-$(($HALF - 1))' $DOWNLOAD_URL > $PARTIAL_FILE"
curl -s "${CURL_HEADERS[@]}" -H "Range: bytes=0-$(($HALF - 1))" "$DOWNLOAD_URL" > "$PARTIAL_FILE"
echo "断点续传，下载剩余部分: $HALF-$(($total_size - 1))"
echo "[调试] curl -s ${CURL_HEADERS[@]} -H 'Range: bytes=$HALF-$(($total_size - 1))' $DOWNLOAD_URL >> $PARTIAL_FILE"
curl -s "${CURL_HEADERS[@]}" -H "Range: bytes=$HALF-$(($total_size - 1))" "$DOWNLOAD_URL" >> "$PARTIAL_FILE"
echo "断点续传测试完成，文件已保存为 $PARTIAL_FILE" 