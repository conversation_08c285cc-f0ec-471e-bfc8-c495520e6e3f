namespace XJ.Framework.Rbac.Domain.Entities;

/// <summary>
/// 岗位 实体
/// </summary>
[Table("positions", Schema = "r")]
[SoftDeleteIndex("IX_Positions_Code", nameof(Code), IsUnique = true)]
[SoftDeleteIndex("IX_Positions_RoleId", nameof(RoleId))]
public class PositionEntity : BaseSoftDeleteEntity<long>
{

    /// <summary>
    /// 角色id
    /// </summary>
    [Column("role_id")]
    public required long RoleId { get; set; }

    /// <summary>
    /// 岗位编码
    /// </summary>
    [Column("code")]
    [StringLength(100)]
    public required string Code { get; set; } = null!;

    /// <summary>
    /// 岗位名称
    /// </summary>
    [Column("name")]
    [StringLength(200)]
    public required string Name { get; set; } = null!;

    /// <summary>
    /// 状态
    /// </summary>
    /// <remarks>
    /// Enabled: 启用
    /// Disabled: 禁用
    /// </remarks>
    [Column("status")]
    public required CommonStatus Status { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    [Column("description")]
    [StringLength(1000)]
    public string? Description { get; set; }
}