namespace XJ.Framework.Rbac.Domain.Entities;

/// <summary>
/// 数据权限规则实体
/// </summary>
[Table("data_permission_rules", Schema = "r")]
[SoftDeleteIndex("IX_data_permission_rules_role_resource", nameof(RoleId), nameof(EntityTypeName))]
public class DataPermissionRuleEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 角色ID
    /// </summary>
    /// <remarks>
    /// 关联角色表的主键
    /// </remarks>
    [Column("role_id")]
    public required long RoleId { get; set; }

    /// <summary>
    /// 实体类型名称
    /// </summary>
    /// <remarks>
    /// 需要进行数据权限控制的实体类型名称
    /// </remarks>
    [Column("entity_type_name")]
    [StringLength(200)]
    public required string EntityTypeName { get; set; } = null!;

    /// <summary>
    /// 规则类型
    /// </summary>
    /// <remarks>
    /// All: 全部数据
    /// Personal: 本人数据
    /// Department: 部门数据
    /// Custom: 自定义规则
    /// </remarks>
    [Column("rule_type")]
    public required DataPermissionRuleType RuleType { get; set; }

    /// <summary>
    /// 规则值
    /// </summary>
    /// <remarks>
    /// 根据规则类型存储对应的规则值：
    /// - 当类型为自定义规则时，可以存储自定义的过滤条件
    /// - 当类型为部门数据时，可以存储部门ID列表
    /// </remarks>
    [Column("rule_value")]
    [StringLength(4000)]
    public string? RuleValue { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    /// <remarks>
    /// Enabled: 启用
    /// Disabled: 禁用
    /// </remarks>
    [Column("status")]
    public required CommonStatus Status { get; set; }

    /// <summary>
    /// 备注说明
    /// </summary>
    /// <remarks>
    /// 用于记录规则的补充说明信息
    /// </remarks>
    [Column("remark")]
    [StringLength(2000)]
    public string? Remark { get; set; }
}