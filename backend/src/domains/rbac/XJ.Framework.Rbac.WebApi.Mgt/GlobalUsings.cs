


global using XJ.Framework.Library.Application.Extensions;
global using XJ.Framework.Library.WebApi;
global using XJ.Framework.Library.WebApi.Controllers;

global using Microsoft.AspNetCore.Mvc;
global using XJ.Framework.Library.Application.Contract.QueryCriteria;
global using XJ.Framework.Library.Domain.Shared.Dtos;
global using XJ.Framework.Library.Domain.UOW;
global using XJ.Framework.Library.WebApi.Extensions;

global using XJ.Framework.Rbac.Application.Contract.Interfaces;
global using XJ.Framework.Rbac.Application.Contract.OperationDtos;
global using XJ.Framework.Rbac.Application.Contract.QueryCriteria;
global using XJ.Framework.Rbac.Domain.Shared.Dtos;
global using XJ.Framework.Rbac.Application;
global using XJ.Framework.Rbac.EntityFrameworkCore;
global using XJ.Framework.Rbac.WebApi;