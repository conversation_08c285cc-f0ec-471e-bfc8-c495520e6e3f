using XJ.Framework.Library.Application.Contract.Interfaces;
using XJ.Framework.Rbac.Application.Extensions;
using XJ.Framework.Rbac.Application.Services;

namespace XJ.Framework.Rbac.Application;

public class RbacApplicationWrapper : ApplicationWrapper
{
    public override void Init(IServiceCollection services, IConfigurationRoot configuration)
    {
        services.AddCaptcha(configuration);
    }
}