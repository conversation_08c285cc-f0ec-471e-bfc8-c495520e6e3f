using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Extensions.Logging;
using XJ.Framework.Library.Domain.Id;
using XJ.Framework.Library.Domain.Shared.Dtos;
using XJ.Framework.Library.Domain.Shared.Enums;
using XJ.Framework.Rbac.Domain;

namespace XJ.Framework.Rbac.Application.Services;

/// <summary>
/// UserExt 服务实现
/// </summary>
public sealed class UserExtService :
    BaseEditableAppService<long, UserExtEntity, UserExtDto, UserExtOperationDto, IUserExtRepository, UserExtQueryCriteria>,
    IUserExtService
{
    public UserExtService(IUserExtRepository repository, IMapper mapper, IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator, ILogger<UserExtService> logger, ICurrentUserContext currentUserContext) : base(
        repository,
        mapper, unitOfWork, keyGenerator, currentUserContext)
    {
        _logger = logger;
    }

    private readonly ILogger<UserExtService> _logger;

    public async Task<bool> RegisterUserExt(RegisterOperationDto dto, long ukey)
    {
        try
        {
            var userext = new UserExtEntity
            {
                ContactAddress = dto.ContactAddress,
                Gender = dto.Gender,
                Country = dto.Country,
                Telephone = dto.Telephone,
                Unit = dto.Unit,
                UserId = ukey,
                Key = IdGenerator.NextId()
            };
            await Repository.InsertAsync(userext);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "用户添加失败: {Username}", dto.Username);
            throw;
        }
    }

    public async Task<UserExtDto> GetUserExtAsync(long userkey)
    {
        var userext = await Repository.GetAsync(q => q.UserId == userkey);
        if (userext == null)
        {
            var dto = new UserExtDto
            {
                UserId = userkey,
                Key = IdGenerator.NextId()
            };
            return dto;
        }
        return Mapper.Map<UserExtDto>(userext);
    }

}
