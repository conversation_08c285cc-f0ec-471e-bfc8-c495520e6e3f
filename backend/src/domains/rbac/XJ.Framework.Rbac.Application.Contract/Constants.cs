using HttpMethod = XJ.Framework.Rbac.Domain.Shared.Enums.HttpMethod;

namespace XJ.Framework.Rbac.Application.Contract;

public static class Constants
{
    public readonly static Dictionary<string, System.Net.Http.HttpMethod> StringToHttpMethodMap = new()
    {
        { "GET", System.Net.Http.HttpMethod.Get },
        { "POST", System.Net.Http.HttpMethod.Post },
        { "PUT", System.Net.Http.HttpMethod.Put },
        { "DELETE", System.Net.Http.HttpMethod.Delete },
        { "PATCH", System.Net.Http.HttpMethod.Patch },
        { "HEAD", System.Net.Http.HttpMethod.Head },
        { "OPTIONS", System.Net.Http.HttpMethod.Options }
    };

    public readonly static Dictionary<string, HttpMethod> StringToEnumMap = new()
    {
        { "GET", HttpMethod.GET },
        { "POST", HttpMethod.POST },
        { "PUT", HttpMethod.PUT },
        { "DELETE", HttpMethod.DELETE },
        { "PATCH", HttpMethod.PATCH },
        { "HEAD", HttpMethod.HEAD },
        { "OPTIONS", HttpMethod.OPTIONS }
    };

    public readonly static Dictionary<System.Net.Http.HttpMethod, HttpMethod> HttpMethodToEnumMap = new()
    {
        { System.Net.Http.HttpMethod.Get, HttpMethod.GET },
        { System.Net.Http.HttpMethod.Post, HttpMethod.POST },
        { System.Net.Http.HttpMethod.Put, HttpMethod.PUT },
        { System.Net.Http.HttpMethod.Delete, HttpMethod.DELETE },
        { System.Net.Http.HttpMethod.Patch, HttpMethod.PATCH },
        { System.Net.Http.HttpMethod.Head, HttpMethod.HEAD },
        { System.Net.Http.HttpMethod.Options, HttpMethod.OPTIONS }
    };

    public readonly static Dictionary<HttpMethod, System.Net.Http.HttpMethod> EnumToHttpMethodMap = new()
    {
        { HttpMethod.GET, System.Net.Http.HttpMethod.Get },
        { HttpMethod.POST, System.Net.Http.HttpMethod.Post },
        { HttpMethod.PUT, System.Net.Http.HttpMethod.Put },
        { HttpMethod.DELETE, System.Net.Http.HttpMethod.Delete },
        { HttpMethod.PATCH, System.Net.Http.HttpMethod.Patch },
        { HttpMethod.HEAD, System.Net.Http.HttpMethod.Head },
        { HttpMethod.OPTIONS, System.Net.Http.HttpMethod.Options }
    };
}