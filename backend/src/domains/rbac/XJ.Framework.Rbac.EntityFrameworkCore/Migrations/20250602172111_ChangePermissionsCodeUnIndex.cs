using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace XJ.Framework.Rbac.EntityFrameworkCore.Migrations
{
    /// <inheritdoc />
    public partial class ChangePermissionsCodeUnIndex : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_permissions_app",
                schema: "r",
                table: "permissions");

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929581731029311492L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929581731029311493L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929581731029311494L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929581731029311495L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929581731029311496L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929581731029311497L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929581731029311498L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929581731029311499L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929581731029311500L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929581731029311501L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929581731029311502L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929581731029311503L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929581731029311504L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929581731029311505L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929581731029311506L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929581731029311507L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929581731029311508L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929581731029311509L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929581731029311510L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929581731029311511L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929581731029311512L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929581731029311513L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929581731029311514L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311515L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311516L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311517L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311518L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311519L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311520L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311521L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311522L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311523L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311524L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311525L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311526L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311527L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311528L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311529L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311530L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311531L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311532L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311533L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311534L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311535L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311536L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311537L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311538L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311539L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311540L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311541L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311542L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311543L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311544L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311545L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311546L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311547L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311548L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311549L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311550L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311551L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311552L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311553L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311554L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311555L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311556L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311557L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311558L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311559L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311560L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311561L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311562L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311563L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311564L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311565L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311566L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311567L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311568L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311569L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311570L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311571L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311572L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311573L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311574L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311575L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311576L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311577L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311578L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311579L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311580L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311581L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311582L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311583L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311584L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929581731029311585L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "user_roles",
                keyColumn: "id",
                keyValue: 1929581731029311488L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "user_roles",
                keyColumn: "id",
                keyValue: 1929581731029311489L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "user_roles",
                keyColumn: "id",
                keyValue: 1929581731029311490L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "user_roles",
                keyColumn: "id",
                keyValue: 1929581731029311491L);

            migrationBuilder.UpdateData(
                schema: "r",
                table: "organizations",
                keyColumn: "id",
                keyValue: 1L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5080), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5080), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "organizations",
                keyColumn: "id",
                keyValue: 2L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5080), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5080), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30027L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30026L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30025L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30024L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30023L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30022L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30021L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30020L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30019L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30018L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30017L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30016L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30015L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30014L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30013L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30012L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30011L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30010L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30009L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30008L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30007L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30006L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30005L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30004L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30003L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30002L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30001L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -1L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 2L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 3L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 4L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 5L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 7L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 8L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 9L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5380), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5390), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 10001L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 10002L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 10003L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 10011L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 10012L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 10013L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 10021L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 10022L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 10023L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 10031L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 10032L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 10033L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 20001L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 20002L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.InsertData(
                schema: "r",
                table: "permissions",
                columns: new[] { "id", "app_code", "app_id", "code", "component", "created_by", "created_time", "is_deleted", "description", "icon", "last_modified_by", "last_modified_time", "method", "name", "parent_id", "path", "redirect", "sort_order", "status", "type" },
                values: new object[,]
                {
                    { 1929589122298834948L, "rbac-mgt", null, "system:user:add", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "添加用户按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), null, "添加用户", 3L, null, null, 1, 1, 3 },
                    { 1929589122298834949L, "rbac-mgt", null, "system:user:edit", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "编辑用户按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), null, "编辑用户", 3L, null, null, 2, 1, 3 },
                    { 1929589122298834950L, "rbac-mgt", null, "system:user:disable", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "禁用用户按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), null, "禁用用户", 3L, null, null, 3, 1, 3 },
                    { 1929589122298834951L, "rbac-mgt", null, "system:user:enable", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "启用用户按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), null, "启用用户", 3L, null, null, 3, 1, 3 },
                    { 1929589122298834952L, "rbac-mgt", null, "system:user:role", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "绑定角色按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), null, "绑定角色", 3L, null, null, 3, 1, 3 },
                    { 1929589122298834953L, "rbac-mgt", null, "system:user:org", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "绑定组织架构按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), null, "绑定组织架构", 3L, null, null, 3, 1, 3 },
                    { 1929589122298834954L, "rbac-mgt", null, "system:role:add", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "添加角色按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), null, "添加角色", 4L, null, null, 1, 1, 3 },
                    { 1929589122298834955L, "rbac-mgt", null, "system:role:edit", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "编辑角色按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), null, "编辑角色", 4L, null, null, 2, 1, 3 },
                    { 1929589122298834956L, "rbac-mgt", null, "system:role:delete", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "删除角色按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), null, "删除角色", 4L, null, null, 3, 1, 3 },
                    { 1929589122298834957L, "rbac-mgt", null, "system:role:permission", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "分配角色权限按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), null, "分配角色权限", 4L, null, null, 3, 1, 3 },
                    { 1929589122298834958L, "rbac-mgt", null, "system:organization:add", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "添加组织按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), null, "添加组织", 5L, null, null, 1, 1, 3 },
                    { 1929589122298834959L, "rbac-mgt", null, "system:organization:edit", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "编辑组织按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), null, "编辑组织", 5L, null, null, 2, 1, 3 },
                    { 1929589122298834960L, "rbac-mgt", null, "system:organization:delete", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "删除组织按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), null, "删除组织", 5L, null, null, 3, 1, 3 },
                    { 1929589122298834961L, "rbac-mgt", null, "system:position:add", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "添加岗位按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), null, "添加岗位", 7L, null, null, 1, 1, 3 },
                    { 1929589122298834962L, "rbac-mgt", null, "system:position:edit", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "编辑岗位按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), null, "编辑岗位", 7L, null, null, 2, 1, 3 },
                    { 1929589122298834963L, "rbac-mgt", null, "system:position:delete", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "删除岗位按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), null, "删除岗位", 7L, null, null, 3, 1, 3 },
                    { 1929589122298834964L, "rbac-mgt", null, "system:permission:add", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "添加权限按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), null, "添加权限", 8L, null, null, 1, 1, 3 },
                    { 1929589122298834965L, "rbac-mgt", null, "system:permission:edit", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "编辑权限按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), null, "编辑权限", 8L, null, null, 2, 1, 3 },
                    { 1929589122298834966L, "rbac-mgt", null, "system:permission:delete", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "删除权限按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), null, "删除权限", 8L, null, null, 3, 1, 3 },
                    { 1929589122298834967L, "dynamic-form-mgt", null, "form:defined:add", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "新增表单按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), null, "新增表单", 20002L, null, null, 1, 1, 3 },
                    { 1929589122298834968L, "dynamic-form-mgt", null, "form:defined:edit", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "编辑表单按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), null, "编辑表单", 20002L, null, null, 2, 1, 3 },
                    { 1929589122298834969L, "dynamic-form-mgt", null, "form:defined:design", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "表单设计按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), null, "表单设计", 20002L, null, null, 3, 1, 3 },
                    { 1929589122298834970L, "dynamic-form-mgt", null, "form:defined:delete", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "删除表单按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), null, "删除表单", 20002L, null, null, 4, 1, 3 }
                });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "positions",
                keyColumn: "id",
                keyValue: 1L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5920), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5920), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "positions",
                keyColumn: "id",
                keyValue: 2L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5920), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5920), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "positions",
                keyColumn: "id",
                keyValue: 3L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5920), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5920), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "positions",
                keyColumn: "id",
                keyValue: 4L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5920), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5920), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.InsertData(
                schema: "r",
                table: "role_permissions",
                columns: new[] { "id", "created_by", "created_time", "is_deleted", "last_modified_by", "last_modified_time", "permission_id", "role_id" },
                values: new object[,]
                {
                    { 1929589122298834971L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 2L, 1L },
                    { 1929589122298834972L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 3L, 1L },
                    { 1929589122298834973L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 4L, 1L },
                    { 1929589122298834974L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 5L, 1L },
                    { 1929589122298834975L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 7L, 1L },
                    { 1929589122298834976L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 8L, 1L },
                    { 1929589122298834977L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 10001L, 1L },
                    { 1929589122298834978L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 10002L, 1L },
                    { 1929589122298834979L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 10003L, 1L },
                    { 1929589122298834980L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 1929589122298834948L, 1L },
                    { 1929589122298834981L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 1929589122298834949L, 1L },
                    { 1929589122298834982L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 1929589122298834950L, 1L },
                    { 1929589122298834983L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 1929589122298834951L, 1L },
                    { 1929589122298834984L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 1929589122298834952L, 1L },
                    { 1929589122298834985L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 1929589122298834953L, 1L },
                    { 1929589122298834986L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 1929589122298834954L, 1L },
                    { 1929589122298834987L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 1929589122298834955L, 1L },
                    { 1929589122298834988L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 1929589122298834956L, 1L },
                    { 1929589122298834989L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 1929589122298834957L, 1L },
                    { 1929589122298834990L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 1929589122298834958L, 1L },
                    { 1929589122298834991L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 1929589122298834959L, 1L },
                    { 1929589122298834992L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 1929589122298834960L, 1L },
                    { 1929589122298834993L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 1929589122298834961L, 1L },
                    { 1929589122298834994L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 1929589122298834962L, 1L },
                    { 1929589122298834995L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 1929589122298834963L, 1L },
                    { 1929589122298834996L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 1929589122298834964L, 1L },
                    { 1929589122298834997L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 1929589122298834965L, 1L },
                    { 1929589122298834998L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 1929589122298834966L, 1L },
                    { 1929589122298834999L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 9L, 1L },
                    { 1929589122298835000L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 10011L, 1L },
                    { 1929589122298835001L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 10012L, 1L },
                    { 1929589122298835002L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 10013L, 1L },
                    { 1929589122298835003L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 10021L, 1L },
                    { 1929589122298835004L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 10022L, 1L },
                    { 1929589122298835005L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 10023L, 1L },
                    { 1929589122298835006L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 10031L, 1L },
                    { 1929589122298835007L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 10032L, 1L },
                    { 1929589122298835008L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 10033L, 1L },
                    { 1929589122298835009L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 20001L, 1L },
                    { 1929589122298835010L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 20002L, 1L },
                    { 1929589122298835011L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 1929589122298834967L, 1L },
                    { 1929589122298835012L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 1929589122298834968L, 1L },
                    { 1929589122298835013L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 1929589122298834969L, 1L },
                    { 1929589122298835014L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), 1929589122298834970L, 1L },
                    { 1929589122298835015L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -1L, 2L },
                    { 1929589122298835016L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -1L, 3L },
                    { 1929589122298835017L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -1L, 4L },
                    { 1929589122298835018L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -1L, 5L },
                    { 1929589122298835019L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30001L, 2L },
                    { 1929589122298835020L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30002L, 2L },
                    { 1929589122298835021L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30003L, 2L },
                    { 1929589122298835022L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30004L, 2L },
                    { 1929589122298835023L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30005L, 2L },
                    { 1929589122298835024L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30006L, 2L },
                    { 1929589122298835025L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30007L, 2L },
                    { 1929589122298835026L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30008L, 2L },
                    { 1929589122298835027L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30009L, 2L },
                    { 1929589122298835028L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30010L, 2L },
                    { 1929589122298835029L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30011L, 3L },
                    { 1929589122298835030L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30012L, 3L },
                    { 1929589122298835031L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30013L, 3L },
                    { 1929589122298835032L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30014L, 3L },
                    { 1929589122298835033L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30015L, 3L },
                    { 1929589122298835034L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30016L, 3L },
                    { 1929589122298835035L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30017L, 3L },
                    { 1929589122298835036L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30018L, 4L },
                    { 1929589122298835037L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30019L, 4L },
                    { 1929589122298835038L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30020L, 4L },
                    { 1929589122298835039L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30021L, 4L },
                    { 1929589122298835040L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30022L, 4L },
                    { 1929589122298835041L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30023L, 4L },
                    { 1929589122298835042L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30024L, 5L },
                    { 1929589122298835043L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30025L, 5L },
                    { 1929589122298835044L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30026L, 5L },
                    { 1929589122298835045L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5250), new TimeSpan(0, 0, 0, 0, 0)), -30027L, 5L }
                });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "roles",
                keyColumn: "id",
                keyValue: 1L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5170), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5170), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "roles",
                keyColumn: "id",
                keyValue: 2L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5170), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5170), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "roles",
                keyColumn: "id",
                keyValue: 3L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5180), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5180), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "roles",
                keyColumn: "id",
                keyValue: 4L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5180), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5180), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "roles",
                keyColumn: "id",
                keyValue: 5L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5180), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5180), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "user_organizations",
                keyColumn: "id",
                keyValue: 1L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5080), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5080), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "user_roles",
                keyColumn: "id",
                keyValue: 1L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5210), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5210), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.InsertData(
                schema: "r",
                table: "user_roles",
                columns: new[] { "id", "created_by", "created_time", "is_deleted", "last_modified_by", "last_modified_time", "organization_id", "position_id", "role_id", "user_id" },
                values: new object[,]
                {
                    { 1929589122298834944L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5230), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5230), new TimeSpan(0, 0, 0, 0, 0)), null, null, 2L, 1L },
                    { 1929589122298834945L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5230), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5230), new TimeSpan(0, 0, 0, 0, 0)), null, null, 3L, 1L },
                    { 1929589122298834946L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5230), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5230), new TimeSpan(0, 0, 0, 0, 0)), null, null, 4L, 1L },
                    { 1929589122298834947L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5240), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(5240), new TimeSpan(0, 0, 0, 0, 0)), null, null, 5L, 1L }
                });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "users",
                keyColumn: "id",
                keyValue: 1L,
                columns: new[] { "created_time", "last_modified_time", "password_hash", "password_salt" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(4960), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 17, 21, 11, 445, DateTimeKind.Unspecified).AddTicks(4970), new TimeSpan(0, 0, 0, 0, 0)), "9z+JdGZpqPannQMz9AV+KI4ErUAXhKjC6ZdAPsP0z/w=", "QVWwozuUltg2v1O3K+M9GQ==" });

            migrationBuilder.CreateIndex(
                name: "IX_permissions_app",
                schema: "r",
                table: "permissions",
                columns: new[] { "app_id", "app_code", "path" },
                filter: "[is_deleted] = 0");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_permissions_app",
                schema: "r",
                table: "permissions");

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929589122298834948L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929589122298834949L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929589122298834950L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929589122298834951L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929589122298834952L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929589122298834953L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929589122298834954L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929589122298834955L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929589122298834956L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929589122298834957L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929589122298834958L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929589122298834959L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929589122298834960L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929589122298834961L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929589122298834962L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929589122298834963L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929589122298834964L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929589122298834965L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929589122298834966L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929589122298834967L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929589122298834968L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929589122298834969L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 1929589122298834970L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834971L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834972L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834973L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834974L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834975L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834976L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834977L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834978L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834979L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834980L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834981L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834982L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834983L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834984L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834985L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834986L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834987L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834988L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834989L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834990L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834991L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834992L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834993L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834994L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834995L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834996L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834997L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834998L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298834999L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835000L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835001L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835002L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835003L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835004L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835005L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835006L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835007L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835008L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835009L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835010L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835011L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835012L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835013L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835014L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835015L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835016L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835017L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835018L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835019L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835020L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835021L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835022L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835023L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835024L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835025L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835026L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835027L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835028L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835029L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835030L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835031L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835032L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835033L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835034L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835035L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835036L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835037L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835038L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835039L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835040L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835041L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835042L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835043L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835044L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "role_permissions",
                keyColumn: "id",
                keyValue: 1929589122298835045L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "user_roles",
                keyColumn: "id",
                keyValue: 1929589122298834944L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "user_roles",
                keyColumn: "id",
                keyValue: 1929589122298834945L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "user_roles",
                keyColumn: "id",
                keyValue: 1929589122298834946L);

            migrationBuilder.DeleteData(
                schema: "r",
                table: "user_roles",
                keyColumn: "id",
                keyValue: 1929589122298834947L);

            migrationBuilder.UpdateData(
                schema: "r",
                table: "organizations",
                keyColumn: "id",
                keyValue: 1L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(210), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(210), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "organizations",
                keyColumn: "id",
                keyValue: 2L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(210), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(210), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30027L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30026L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30025L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30024L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30023L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30022L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30021L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30020L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30019L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30018L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30017L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30016L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30015L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30014L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30013L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30012L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30011L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30010L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30009L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30008L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30007L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30006L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30005L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30004L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30003L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30002L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -30001L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: -1L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 2L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 3L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 4L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 5L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 7L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 8L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 9L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(460), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(460), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 10001L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 10002L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 10003L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 10011L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 10012L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 10013L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 10021L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 10022L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 10023L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 10031L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 10032L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 10033L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 20001L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "permissions",
                keyColumn: "id",
                keyValue: 20002L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.InsertData(
                schema: "r",
                table: "permissions",
                columns: new[] { "id", "app_code", "app_id", "code", "component", "created_by", "created_time", "is_deleted", "description", "icon", "last_modified_by", "last_modified_time", "method", "name", "parent_id", "path", "redirect", "sort_order", "status", "type" },
                values: new object[,]
                {
                    { 1929581731029311492L, "rbac-mgt", null, "system:user:add", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "添加用户按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), null, "添加用户", 3L, null, null, 1, 1, 3 },
                    { 1929581731029311493L, "rbac-mgt", null, "system:user:edit", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "编辑用户按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), null, "编辑用户", 3L, null, null, 2, 1, 3 },
                    { 1929581731029311494L, "rbac-mgt", null, "system:user:disable", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "禁用用户按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), null, "禁用用户", 3L, null, null, 3, 1, 3 },
                    { 1929581731029311495L, "rbac-mgt", null, "system:user:enable", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "启用用户按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), null, "启用用户", 3L, null, null, 3, 1, 3 },
                    { 1929581731029311496L, "rbac-mgt", null, "system:user:role", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "绑定角色按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), null, "绑定角色", 3L, null, null, 3, 1, 3 },
                    { 1929581731029311497L, "rbac-mgt", null, "system:user:org", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "绑定组织架构按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), null, "绑定组织架构", 3L, null, null, 3, 1, 3 },
                    { 1929581731029311498L, "rbac-mgt", null, "system:role:add", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "添加角色按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), null, "添加角色", 4L, null, null, 1, 1, 3 },
                    { 1929581731029311499L, "rbac-mgt", null, "system:role:edit", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "编辑角色按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), null, "编辑角色", 4L, null, null, 2, 1, 3 },
                    { 1929581731029311500L, "rbac-mgt", null, "system:role:delete", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "删除角色按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), null, "删除角色", 4L, null, null, 3, 1, 3 },
                    { 1929581731029311501L, "rbac-mgt", null, "system:role:permission", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "分配角色权限按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), null, "分配角色权限", 4L, null, null, 3, 1, 3 },
                    { 1929581731029311502L, "rbac-mgt", null, "system:organization:add", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "添加组织按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), null, "添加组织", 5L, null, null, 1, 1, 3 },
                    { 1929581731029311503L, "rbac-mgt", null, "system:organization:edit", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "编辑组织按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), null, "编辑组织", 5L, null, null, 2, 1, 3 },
                    { 1929581731029311504L, "rbac-mgt", null, "system:organization:delete", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "删除组织按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), null, "删除组织", 5L, null, null, 3, 1, 3 },
                    { 1929581731029311505L, "rbac-mgt", null, "system:position:add", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "添加岗位按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), null, "添加岗位", 7L, null, null, 1, 1, 3 },
                    { 1929581731029311506L, "rbac-mgt", null, "system:position:edit", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "编辑岗位按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), null, "编辑岗位", 7L, null, null, 2, 1, 3 },
                    { 1929581731029311507L, "rbac-mgt", null, "system:position:delete", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "删除岗位按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), null, "删除岗位", 7L, null, null, 3, 1, 3 },
                    { 1929581731029311508L, "rbac-mgt", null, "system:permission:add", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "添加权限按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), null, "添加权限", 8L, null, null, 1, 1, 3 },
                    { 1929581731029311509L, "rbac-mgt", null, "system:permission:edit", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "编辑权限按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), null, "编辑权限", 8L, null, null, 2, 1, 3 },
                    { 1929581731029311510L, "rbac-mgt", null, "system:permission:delete", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "删除权限按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), null, "删除权限", 8L, null, null, 3, 1, 3 },
                    { 1929581731029311511L, "dynamic-form-mgt", null, "form:defined:add", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "新增表单按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), null, "新增表单", 20002L, null, null, 1, 1, 3 },
                    { 1929581731029311512L, "dynamic-form-mgt", null, "form:defined:edit", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "编辑表单按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), null, "编辑表单", 20002L, null, null, 2, 1, 3 },
                    { 1929581731029311513L, "dynamic-form-mgt", null, "form:defined:design", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "表单设计按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), null, "表单设计", 20002L, null, null, 3, 1, 3 },
                    { 1929581731029311514L, "dynamic-form-mgt", null, "form:defined:delete", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "删除表单按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), null, "删除表单", 20002L, null, null, 4, 1, 3 }
                });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "positions",
                keyColumn: "id",
                keyValue: 1L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(930), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(930), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "positions",
                keyColumn: "id",
                keyValue: 2L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(930), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(930), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "positions",
                keyColumn: "id",
                keyValue: 3L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(930), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(930), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "positions",
                keyColumn: "id",
                keyValue: 4L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(930), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(930), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.InsertData(
                schema: "r",
                table: "role_permissions",
                columns: new[] { "id", "created_by", "created_time", "is_deleted", "last_modified_by", "last_modified_time", "permission_id", "role_id" },
                values: new object[,]
                {
                    { 1929581731029311515L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 2L, 1L },
                    { 1929581731029311516L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 3L, 1L },
                    { 1929581731029311517L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 4L, 1L },
                    { 1929581731029311518L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 5L, 1L },
                    { 1929581731029311519L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 7L, 1L },
                    { 1929581731029311520L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 8L, 1L },
                    { 1929581731029311521L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 10001L, 1L },
                    { 1929581731029311522L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 10002L, 1L },
                    { 1929581731029311523L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 10003L, 1L },
                    { 1929581731029311524L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 1929581731029311492L, 1L },
                    { 1929581731029311525L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 1929581731029311493L, 1L },
                    { 1929581731029311526L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 1929581731029311494L, 1L },
                    { 1929581731029311527L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 1929581731029311495L, 1L },
                    { 1929581731029311528L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 1929581731029311496L, 1L },
                    { 1929581731029311529L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 1929581731029311497L, 1L },
                    { 1929581731029311530L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 1929581731029311498L, 1L },
                    { 1929581731029311531L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 1929581731029311499L, 1L },
                    { 1929581731029311532L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 1929581731029311500L, 1L },
                    { 1929581731029311533L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 1929581731029311501L, 1L },
                    { 1929581731029311534L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 1929581731029311502L, 1L },
                    { 1929581731029311535L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 1929581731029311503L, 1L },
                    { 1929581731029311536L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 1929581731029311504L, 1L },
                    { 1929581731029311537L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 1929581731029311505L, 1L },
                    { 1929581731029311538L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 1929581731029311506L, 1L },
                    { 1929581731029311539L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 1929581731029311507L, 1L },
                    { 1929581731029311540L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 1929581731029311508L, 1L },
                    { 1929581731029311541L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 1929581731029311509L, 1L },
                    { 1929581731029311542L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 1929581731029311510L, 1L },
                    { 1929581731029311543L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 9L, 1L },
                    { 1929581731029311544L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 10011L, 1L },
                    { 1929581731029311545L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 10012L, 1L },
                    { 1929581731029311546L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 10013L, 1L },
                    { 1929581731029311547L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 10021L, 1L },
                    { 1929581731029311548L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 10022L, 1L },
                    { 1929581731029311549L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 10023L, 1L },
                    { 1929581731029311550L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 10031L, 1L },
                    { 1929581731029311551L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 10032L, 1L },
                    { 1929581731029311552L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 10033L, 1L },
                    { 1929581731029311553L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 20001L, 1L },
                    { 1929581731029311554L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 20002L, 1L },
                    { 1929581731029311555L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 1929581731029311511L, 1L },
                    { 1929581731029311556L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 1929581731029311512L, 1L },
                    { 1929581731029311557L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 1929581731029311513L, 1L },
                    { 1929581731029311558L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), 1929581731029311514L, 1L },
                    { 1929581731029311559L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30001L, 2L },
                    { 1929581731029311560L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30002L, 2L },
                    { 1929581731029311561L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30003L, 2L },
                    { 1929581731029311562L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30004L, 2L },
                    { 1929581731029311563L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30005L, 2L },
                    { 1929581731029311564L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30006L, 2L },
                    { 1929581731029311565L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30007L, 2L },
                    { 1929581731029311566L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30008L, 2L },
                    { 1929581731029311567L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30009L, 2L },
                    { 1929581731029311568L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30010L, 2L },
                    { 1929581731029311569L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30011L, 3L },
                    { 1929581731029311570L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30012L, 3L },
                    { 1929581731029311571L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30013L, 3L },
                    { 1929581731029311572L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30014L, 3L },
                    { 1929581731029311573L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30015L, 3L },
                    { 1929581731029311574L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30016L, 3L },
                    { 1929581731029311575L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30017L, 3L },
                    { 1929581731029311576L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30018L, 4L },
                    { 1929581731029311577L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30019L, 4L },
                    { 1929581731029311578L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30020L, 4L },
                    { 1929581731029311579L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30021L, 4L },
                    { 1929581731029311580L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30022L, 4L },
                    { 1929581731029311581L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30023L, 4L },
                    { 1929581731029311582L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30024L, 5L },
                    { 1929581731029311583L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30025L, 5L },
                    { 1929581731029311584L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30026L, 5L },
                    { 1929581731029311585L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(350), new TimeSpan(0, 0, 0, 0, 0)), -30027L, 5L }
                });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "roles",
                keyColumn: "id",
                keyValue: 1L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(280), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(280), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "roles",
                keyColumn: "id",
                keyValue: 2L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(280), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(280), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "roles",
                keyColumn: "id",
                keyValue: 3L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(290), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(290), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "roles",
                keyColumn: "id",
                keyValue: 4L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(290), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(290), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "roles",
                keyColumn: "id",
                keyValue: 5L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(290), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(290), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "user_organizations",
                keyColumn: "id",
                keyValue: 1L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(210), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(210), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "user_roles",
                keyColumn: "id",
                keyValue: 1L,
                columns: new[] { "created_time", "last_modified_time" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(310), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(310), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.InsertData(
                schema: "r",
                table: "user_roles",
                columns: new[] { "id", "created_by", "created_time", "is_deleted", "last_modified_by", "last_modified_time", "organization_id", "position_id", "role_id", "user_id" },
                values: new object[,]
                {
                    { 1929581731029311488L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(330), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(330), new TimeSpan(0, 0, 0, 0, 0)), null, null, 2L, 1L },
                    { 1929581731029311489L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(330), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(330), new TimeSpan(0, 0, 0, 0, 0)), null, null, 3L, 1L },
                    { 1929581731029311490L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(340), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(340), new TimeSpan(0, 0, 0, 0, 0)), null, null, 4L, 1L },
                    { 1929581731029311491L, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(340), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(340), new TimeSpan(0, 0, 0, 0, 0)), null, null, 5L, 1L }
                });

            migrationBuilder.UpdateData(
                schema: "r",
                table: "users",
                keyColumn: "id",
                keyValue: 1L,
                columns: new[] { "created_time", "last_modified_time", "password_hash", "password_salt" },
                values: new object[] { new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(120), new TimeSpan(0, 0, 0, 0, 0)), new DateTimeOffset(new DateTime(2025, 6, 2, 16, 51, 49, 229, DateTimeKind.Unspecified).AddTicks(130), new TimeSpan(0, 0, 0, 0, 0)), "THwHmgeY4EbJZJEJsFljhEYmKlsOpc51UkYQCfK3UTE=", "yHTN0jWAOGk09Pwx67IZ0g==" });

            migrationBuilder.CreateIndex(
                name: "IX_permissions_app",
                schema: "r",
                table: "permissions",
                columns: new[] { "app_id", "app_code" },
                filter: "[is_deleted] = 0");
        }
    }
}
