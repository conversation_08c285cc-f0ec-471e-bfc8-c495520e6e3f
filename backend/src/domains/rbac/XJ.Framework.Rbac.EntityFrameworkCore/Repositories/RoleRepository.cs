using XJ.Framework.Library.Domain.Shared.Enums;
using XJ.Framework.Rbac.Domain.Shared.Enums;

namespace XJ.Framework.Rbac.EntityFrameworkCore.Repositories;

/// <summary>
/// Role 仓储实现
/// </summary>
public class RoleRepository : BaseAuditRepository<RbacDbContext, long, RoleEntity>, IRoleRepository
{
    public RoleRepository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public async Task<List<long>> GetUserPositionRoleIdsAsync(long userId)
    {
        /*
         * 我需要关联几个表得到数据
         * 1.通过userId读取userPosition表  Status=UserPositionStatus.Active 用户id=userId 如果StartTime和EndTime有值 则当前时间 要在这个范围内
         * 2.使用userPosition表的positionId 关联到position表 读取所有的角色id
         * 3.使用position表的roleId 关联到role表 读取所有的角色id
         */
        // 读取用户有效岗位id集合  Status=UserPositionStatus.Active 用户id=userId 如果StartTime和EndTime有值 则当前时间 要在这个范围内

        var roleIds = await (from up in DbContext.Set<UserPositionEntity>()
                join p in DbContext.Set<PositionEntity>() on up.PositionId equals p.Key
                join r in DbContext.Set<RoleEntity>() on p.RoleId equals r.Key
                where up.UserId == userId && p.Status == CommonStatus.Enabled && r.Status == CommonStatus.Enabled &&
                      r.Type == RoleType.Position &&
                      up.Status == UserPositionStatus.Active
                      && ((up.StartTime.HasValue && up.EndTime.HasValue &&
                           up.StartTime.Value <= DateTime.Now && up.EndTime.Value >= DateTime.Now)
                          || (!up.StartTime.HasValue && !up.EndTime.HasValue))
                      && !up.Deleted && !p.Deleted && !r.Deleted
                select r.Key
            ).ToListAsync();
        return roleIds;
    }
}