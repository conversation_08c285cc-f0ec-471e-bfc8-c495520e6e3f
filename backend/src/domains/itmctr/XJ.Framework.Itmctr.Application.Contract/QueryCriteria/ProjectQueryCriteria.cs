using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.DynamicForm.Domain.Shared.Enums;
using XJ.Framework.Itmctr.Domain.Shared.Dtos;

namespace XJ.Framework.Itmctr.Application.Contract.QueryCriteria;

/// <summary>
/// 项目 查询条件
/// </summary>
public class ProjectQueryCriteria : BaseQueryCriteria
{
    public string businessId { get; set; }
    public Dictionary<string, string> DynamicQueries { get; set; } = new();
}