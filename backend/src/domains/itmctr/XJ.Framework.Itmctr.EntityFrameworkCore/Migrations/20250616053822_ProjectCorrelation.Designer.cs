// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using XJ.Framework.Itmctr.EntityFrameworkCore;

#nullable disable

namespace XJ.Framework.Itmctr.EntityFrameworkCore.Migrations
{
    [DbContext(typeof(ItmctrDbContext))]
    [Migration("20250616053822_ProjectCorrelation")]
    partial class ProjectCorrelation
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("XJ.Framework.Itmctr.Domain.Entities.AsyncTaskEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<long>("ApplyUserId")
                        .HasColumnType("bigint")
                        .HasColumnName("apply_user_id")
                        .HasComment("发起人id");

                    b.Property<string>("BusinessId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("business_id")
                        .HasComment("业务id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<string>("TaskCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("task_code")
                        .HasComment("任务编码");

                    b.Property<string>("TaskData")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("task_data")
                        .HasComment("任务数据");

                    b.Property<string>("TaskResult")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("task_result")
                        .HasComment("任务结果");

                    b.Property<int>("TaskStatus")
                        .HasColumnType("int")
                        .HasColumnName("task_status")
                        .HasComment("任务数据");

                    b.HasKey("Key");

                    b.ToTable("async_tasks", "i", t =>
                        {
                            t.HasComment("异步任务");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Itmctr.Domain.Entities.ProjectAttachEntity", b =>
                {
                    b.Property<Guid>("Key")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("BusinessId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("business_id")
                        .HasComment("关联业务id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(510)
                        .HasColumnType("nvarchar(510)")
                        .HasColumnName("file_name")
                        .HasComment("文件原始名称");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint")
                        .HasColumnName("file_size")
                        .HasComment("文件总大小（字节）");

                    b.Property<string>("FileTypeCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("file_type_code")
                        .HasComment("文件类型code");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<long>("ProjectId")
                        .HasColumnType("bigint")
                        .HasColumnName("project_id")
                        .HasComment("项目ID");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("version")
                        .HasComment("版本");

                    b.HasKey("Key");

                    b.HasIndex("BusinessId")
                        .HasDatabaseName("IX_ProjectAttach_BusinessId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("FileName")
                        .HasDatabaseName("IX_ProjectAttach_FileName")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("FileTypeCode")
                        .HasDatabaseName("IX_ProjectAttach_FileTypeCode")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("ProjectId")
                        .HasDatabaseName("IX_ProjectAttach_ProjectId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("Version")
                        .HasDatabaseName("IX_ProjectAttach_Version")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("project_attach", "i", t =>
                        {
                            t.HasComment("项目基本信息附件");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Itmctr.Domain.Entities.ProjectAttachHistoryEntity", b =>
                {
                    b.Property<Guid>("Key")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("BusinessId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("business_id")
                        .HasComment("关联业务id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(510)
                        .HasColumnType("nvarchar(510)")
                        .HasColumnName("file_name")
                        .HasComment("文件原始名称");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint")
                        .HasColumnName("file_size")
                        .HasComment("文件总大小（字节）");

                    b.Property<string>("FileTypeCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("file_type_code")
                        .HasComment("文件类型code");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<long>("ProjectId")
                        .HasColumnType("bigint")
                        .HasColumnName("project_id")
                        .HasComment("项目ID");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("version")
                        .HasComment("版本");

                    b.HasKey("Key");

                    b.HasIndex("BusinessId")
                        .HasDatabaseName("IX_ProjectAttachHistory_BusinessId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("FileName")
                        .HasDatabaseName("IX_ProjectAttachHistory_FileName")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("FileTypeCode")
                        .HasDatabaseName("IX_ProjectAttachHistory_FileTypeCode")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("ProjectId")
                        .HasDatabaseName("IX_ProjectAttachHistory_ProjectId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("Version")
                        .HasDatabaseName("IX_ProjectAttachHistory_Version")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("project_attach_history", "i", t =>
                        {
                            t.HasComment("项目基本信息附件历史记录");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Itmctr.Domain.Entities.ProjectEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("BusinessId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("business_id")
                        .HasComment("关联业务id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("FormCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("form_code")
                        .HasComment("表单code");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("version")
                        .HasComment("版本号");

                    b.Property<int?>("age_range_max")
                        .HasColumnType("int")
                        .HasColumnName("age_range_max")
                        .HasComment("年龄范围结束");

                    b.Property<int?>("age_range_min")
                        .HasColumnType("int")
                        .HasColumnName("age_range_min")
                        .HasComment("年龄范围开始");

                    b.Property<string>("allocation_concealment_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("allocation_concealment_En")
                        .HasComment("隐蔽分组方法和过程（英文）");

                    b.Property<string>("allocation_concealment_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("allocation_concealment_Zh")
                        .HasComment("隐蔽分组方法和过程（中文）");

                    b.Property<string>("applicant_address_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("applicant_address_En")
                        .HasComment("申请注册联系人通讯地址（英文）");

                    b.Property<string>("applicant_address_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("applicant_address_Zh")
                        .HasComment("申请注册联系人通讯地址（中文）");

                    b.Property<string>("applicant_affiliation_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("applicant_affiliation_En")
                        .HasComment("申请人所在单位（英文）");

                    b.Property<string>("applicant_affiliation_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("applicant_affiliation_Zh")
                        .HasComment("申请人所在单位（中文）");

                    b.Property<string>("applicant_email")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("applicant_email")
                        .HasComment("申请注册联系人电子邮件");

                    b.Property<string>("applicant_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("applicant_En")
                        .HasComment("申请注册联系人（英文）");

                    b.Property<string>("applicant_fax")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("applicant_fax")
                        .HasComment("申请注册联系人传真");

                    b.Property<string>("applicant_postcode")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("applicant_postcode")
                        .HasComment("申请注册联系人邮政编码");

                    b.Property<string>("applicant_telephone")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("applicant_telephone")
                        .HasComment("申请注册联系人电话");

                    b.Property<string>("applicant_website")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("applicant_website")
                        .HasComment("申请单位网址(自愿提供)");

                    b.Property<string>("applicant_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("applicant_Zh")
                        .HasComment("申请注册联系人（中文）");

                    b.Property<string>("blinding_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("blinding_En")
                        .HasComment("盲法（英文）");

                    b.Property<string>("blinding_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("blinding_Zh")
                        .HasComment("盲法（中文）");

                    b.Property<string>("calculated_results_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("calculated_results_En")
                        .HasComment("试验完成后的统计结果（英文）");

                    b.Property<string>("calculated_results_public")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("calculated_results_public")
                        .HasComment("是否公开试验完成后的统计结果");

                    b.Property<string>("calculated_results_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("calculated_results_Zh")
                        .HasComment("试验完成后的统计结果（中文）");

                    b.Property<string>("confounding_condition_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("confounding_condition_En")
                        .HasComment("容易混淆的疾病人群（英文）");

                    b.Property<string>("confounding_condition_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("confounding_condition_Zh")
                        .HasComment("容易混淆的疾病人群（中文）");

                    b.Property<string>("confounding_sample_size")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("confounding_sample_size")
                        .HasComment("容易混淆的疾病人群例数");

                    b.Property<string>("data_collection_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("data_collection_En")
                        .HasComment("数据采集和管理（英文）");

                    b.Property<string>("data_collection_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("data_collection_Zh")
                        .HasComment("数据采集和管理（中文）");

                    b.Property<string>("english_acronym_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("english_acronym_En")
                        .HasComment("注册题目简写英文");

                    b.Property<string>("english_acronym_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("english_acronym_Zh")
                        .HasComment("注册题目简写中文");

                    b.Property<string>("ethic_committee_address_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ethic_committee_address_En")
                        .HasComment("伦理委员会联系地址（英文）");

                    b.Property<string>("ethic_committee_address_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ethic_committee_address_Zh")
                        .HasComment("伦理委员会联系地址（中文）");

                    b.Property<string>("ethic_committee_approved")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ethic_committee_approved")
                        .HasComment("是否获伦理委员会批准");

                    b.Property<DateTime?>("ethic_committee_approved_date")
                        .HasColumnType("datetime2")
                        .HasColumnName("ethic_committee_approved_date")
                        .HasComment("伦理委员会批准日期");

                    b.Property<string>("ethic_committee_approved_no")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ethic_committee_approved_no")
                        .HasComment("伦理委员会批件文号");

                    b.Property<string>("ethic_committee_contact_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ethic_committee_contact_En")
                        .HasComment("伦理委员会联系人（英文）");

                    b.Property<string>("ethic_committee_contact_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ethic_committee_contact_Zh")
                        .HasComment("伦理委员会联系人（中文）");

                    b.Property<string>("ethic_committee_email")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ethic_committee_email")
                        .HasComment("伦理委员会联系人邮箱");

                    b.Property<string>("ethic_committee_name_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ethic_committee_name_En")
                        .HasComment("批准本研究的伦理委员会名称（英文）");

                    b.Property<string>("ethic_committee_name_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ethic_committee_name_Zh")
                        .HasComment("批准本研究的伦理委员会名称（中文）");

                    b.Property<string>("ethic_committee_phone")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ethic_committee_phone")
                        .HasComment("伦理委员会联系人电话");

                    b.Property<string>("exclusion_criteria_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("exclusion_criteria_En")
                        .HasComment("排除标准（英文）");

                    b.Property<string>("exclusion_criteria_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("exclusion_criteria_Zh")
                        .HasComment("排除标准（中文）");

                    b.Property<string>("follow_up_length")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("follow_up_length")
                        .HasComment("随访时间");

                    b.Property<string>("follow_up_unit")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("follow_up_unit")
                        .HasComment("随访时间单位");

                    b.Property<string>("funding_source_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("funding_source_En")
                        .HasComment("经费或物资来源（英文）");

                    b.Property<string>("funding_source_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("funding_source_Zh")
                        .HasComment("经费或物资来源（中文）");

                    b.Property<string>("gender")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("gender")
                        .HasComment("性别");

                    b.Property<string>("gold_standard_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("gold_standard_En")
                        .HasComment("金标准或参考标准（英文）");

                    b.Property<string>("gold_standard_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("gold_standard_Zh")
                        .HasComment("金标准或参考标准（中文）");

                    b.Property<string>("inclusion_criteria_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("inclusion_criteria_En")
                        .HasComment("纳入标准（英文）");

                    b.Property<string>("inclusion_criteria_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("inclusion_criteria_Zh")
                        .HasComment("纳入标准（中文）");

                    b.Property<string>("index_test_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("index_test_En")
                        .HasComment("指标试验（英文）");

                    b.Property<string>("index_test_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("index_test_Zh")
                        .HasComment("指标试验（中文）");

                    b.Property<string>("intervention_total_sample_size")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("intervention_total_sample_size")
                        .HasComment("干预措施样本总量");

                    b.Property<string>("ipd_sharing")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("ipd_sharing")
                        .HasComment("是否共享原始数据");

                    b.Property<string>("ipd_sharing_way_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ipd_sharing_way_En")
                        .HasComment("共享原始数据的方式（英文）");

                    b.Property<string>("ipd_sharing_way_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ipd_sharing_way_Zh")
                        .HasComment("共享原始数据的方式（中文）");

                    b.Property<string>("language")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("language")
                        .HasComment("语言");

                    b.Property<DateTime?>("mpa_approved_date")
                        .HasColumnType("datetime2")
                        .HasColumnName("mpa_approved_date")
                        .HasComment("国家药监局批准日期");

                    b.Property<string>("mpa_approved_no")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("mpa_approved_no")
                        .HasComment("国家药监局批准文号");

                    b.Property<string>("partner_registry_number")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("partner_registry_number")
                        .HasComment("在二级注册机构或其它机构的注册号");

                    b.Property<string>("primary_sponsor_address_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("primary_sponsor_address_En")
                        .HasComment("研究实施负责（组长）单位地址（英文）");

                    b.Property<string>("primary_sponsor_address_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("primary_sponsor_address_Zh")
                        .HasComment("研究实施负责（组长）单位地址（中文）");

                    b.Property<string>("primary_sponsor_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("primary_sponsor_En")
                        .HasComment("研究实施负责（组长）单位（英文）");

                    b.Property<string>("primary_sponsor_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("primary_sponsor_Zh")
                        .HasComment("研究实施负责（组长）单位（中文）");

                    b.Property<string>("publication_info_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("publication_info_En")
                        .HasComment("研究计划书或研究结果报告发表信息（英文）");

                    b.Property<string>("publication_info_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("publication_info_Zh")
                        .HasComment("研究计划书或研究结果报告发表信息（中文）");

                    b.Property<string>("publictitle_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("publictitle_En")
                        .HasComment("注册题目英文");

                    b.Property<string>("publictitle_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("publictitle_Zh")
                        .HasComment("注册题目中文");

                    b.Property<string>("randomization_procedure_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("randomization_procedure_En")
                        .HasComment("随机方法（请说明由何人用什么方法产生随机序列）（英文）");

                    b.Property<string>("randomization_procedure_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("randomization_procedure_Zh")
                        .HasComment("随机方法（请说明由何人用什么方法产生随机序列）（中文）");

                    b.Property<string>("recruiting_status")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("recruiting_status")
                        .HasComment("征募研究对象状况");

                    b.Property<DateTime?>("recruiting_time_end")
                        .HasMaxLength(500)
                        .HasColumnType("datetime2")
                        .HasColumnName("recruiting_time_end")
                        .HasComment("征募观察对象时间结束");

                    b.Property<DateTime?>("recruiting_time_start")
                        .HasMaxLength(500)
                        .HasColumnType("datetime2")
                        .HasColumnName("recruiting_time_start")
                        .HasComment("征募观察对象时间开始");

                    b.Property<string>("registration_number")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("registration_number")
                        .HasComment("注册号");

                    b.Property<string>("registration_status")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("registration_status")
                        .HasComment("注册号状态");

                    b.Property<string>("safety_committee")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("safety_committee")
                        .HasComment("数据与安全监察委员会");

                    b.Property<string>("scientific_title_acronym_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("scientific_title_acronym_En")
                        .HasComment("研究课题的正式科学名称简写（英文）");

                    b.Property<string>("scientific_title_acronym_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("scientific_title_acronym_Zh")
                        .HasComment("研究课题的正式科学名称简写（中文）");

                    b.Property<string>("scientific_title_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("scientific_title_En")
                        .HasComment("研究课题的正式科学名称英文");

                    b.Property<string>("scientific_title_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("scientific_title_Zh")
                        .HasComment("研究课题的正式科学名称中文");

                    b.Property<string>("sign_informed_consent")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("sign_informed_consent")
                        .HasComment("研究对象是否签署知情同意书");

                    b.Property<string>("statistical_methods_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("statistical_methods_En")
                        .HasComment("统计方法名称（英文）");

                    b.Property<string>("statistical_methods_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("statistical_methods_Zh")
                        .HasComment("统计方法名称（中文）");

                    b.Property<string>("study_design")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_design")
                        .HasComment("研究设计");

                    b.Property<string>("study_leader_address_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_leader_address_En")
                        .HasComment("研究负责人通讯地址（英文）");

                    b.Property<string>("study_leader_address_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_leader_address_Zh")
                        .HasComment("研究负责人通讯地址（中文）");

                    b.Property<string>("study_leader_affiliation_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_leader_affiliation_En")
                        .HasComment("研究负责人所在单位（英文）");

                    b.Property<string>("study_leader_affiliation_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_leader_affiliation_Zh")
                        .HasComment("研究负责人所在单位（中文）");

                    b.Property<string>("study_leader_email")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_leader_email")
                        .HasComment("研究负责人电子邮件");

                    b.Property<string>("study_leader_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_leader_En")
                        .HasComment("研究负责人（英文）");

                    b.Property<string>("study_leader_fax")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_leader_fax")
                        .HasComment("研究负责人传真");

                    b.Property<string>("study_leader_postcode")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_leader_postcode")
                        .HasComment("研究负责人邮政编码");

                    b.Property<string>("study_leader_telephone")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_leader_telephone")
                        .HasComment("研究负责人电话");

                    b.Property<string>("study_leader_website")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_leader_website")
                        .HasComment("研究负责人网址(自愿提供)");

                    b.Property<string>("study_leader_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_leader_Zh")
                        .HasComment("研究负责人（中文）");

                    b.Property<string>("study_objectives_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_objectives_En")
                        .HasComment("研究目的（英文）");

                    b.Property<string>("study_objectives_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_objectives_Zh")
                        .HasComment("研究目的（中文）");

                    b.Property<string>("study_phase")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_phase")
                        .HasComment("研究所处阶段");

                    b.Property<string>("study_subject_id")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_subject_id")
                        .HasComment("研究课题代号(代码)");

                    b.Property<DateTime?>("study_time_end")
                        .HasMaxLength(500)
                        .HasColumnType("datetime2")
                        .HasColumnName("study_time_end")
                        .HasComment("研究实施时间结束");

                    b.Property<DateTime?>("study_time_start")
                        .HasMaxLength(500)
                        .HasColumnType("datetime2")
                        .HasColumnName("study_time_start")
                        .HasComment("研究实施时间开始");

                    b.Property<string>("study_type")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_type")
                        .HasComment("研究类型");

                    b.Property<string>("target_condition_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("target_condition_En")
                        .HasComment("目标人群（英文）");

                    b.Property<string>("target_condition_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("target_condition_Zh")
                        .HasComment("目标人群（中文）");

                    b.Property<string>("target_disease_code")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("target_disease_code")
                        .HasComment("研究疾病代码");

                    b.Property<string>("target_disease_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("target_disease_En")
                        .HasComment("研究疾病（英文）");

                    b.Property<string>("target_disease_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("target_disease_Zh")
                        .HasComment("研究疾病（中文）");

                    b.Property<string>("target_sample_size")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("target_sample_size")
                        .HasComment("目标人群例数");

                    b.Property<string>("treatment_description_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("treatment_description_En")
                        .HasComment("药物成份或治疗方案详述（英文）");

                    b.Property<string>("treatment_description_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("treatment_description_Zh")
                        .HasComment("药物成份或治疗方案详述（中文）");

                    b.Property<string>("unblinding_rules_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("unblinding_rules_En")
                        .HasComment("揭盲或破盲原则和方法（英文）");

                    b.Property<string>("unblinding_rules_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("unblinding_rules_Zh")
                        .HasComment("揭盲或破盲原则和方法（中文）");

                    b.Property<string>("utn")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("utn")
                        .HasComment("全球唯一识别码");

                    b.HasKey("Key");

                    b.HasIndex("BusinessId")
                        .HasDatabaseName("IX_Project_BusinessId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("FormCode")
                        .HasDatabaseName("IX_Project_FormCode")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("Version")
                        .HasDatabaseName("IX_Project_Version")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("projects", "i", t =>
                        {
                            t.HasComment("项目信息");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Itmctr.Domain.Entities.ProjectHistoryEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("BusinessId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("business_id")
                        .HasComment("关联业务id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("FormCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("form_code")
                        .HasComment("表单code");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("version")
                        .HasComment("版本号");

                    b.Property<int?>("age_range_max")
                        .HasColumnType("int")
                        .HasColumnName("age_range_max")
                        .HasComment("年龄范围结束");

                    b.Property<int?>("age_range_min")
                        .HasColumnType("int")
                        .HasColumnName("age_range_min")
                        .HasComment("年龄范围开始");

                    b.Property<string>("allocation_concealment_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("allocation_concealment_En")
                        .HasComment("隐蔽分组方法和过程（英文）");

                    b.Property<string>("allocation_concealment_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("allocation_concealment_Zh")
                        .HasComment("隐蔽分组方法和过程（中文）");

                    b.Property<string>("applicant_address_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("applicant_address_En")
                        .HasComment("申请注册联系人通讯地址（英文）");

                    b.Property<string>("applicant_address_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("applicant_address_Zh")
                        .HasComment("申请注册联系人通讯地址（中文）");

                    b.Property<string>("applicant_affiliation_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("applicant_affiliation_En")
                        .HasComment("申请人所在单位（英文）");

                    b.Property<string>("applicant_affiliation_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("applicant_affiliation_Zh")
                        .HasComment("申请人所在单位（中文）");

                    b.Property<string>("applicant_email")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("applicant_email")
                        .HasComment("申请注册联系人电子邮件");

                    b.Property<string>("applicant_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("applicant_En")
                        .HasComment("申请注册联系人（英文）");

                    b.Property<string>("applicant_fax")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("applicant_fax")
                        .HasComment("申请注册联系人传真");

                    b.Property<string>("applicant_postcode")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("applicant_postcode")
                        .HasComment("申请注册联系人邮政编码");

                    b.Property<string>("applicant_telephone")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("applicant_telephone")
                        .HasComment("申请注册联系人电话");

                    b.Property<string>("applicant_website")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("applicant_website")
                        .HasComment("申请单位网址(自愿提供)");

                    b.Property<string>("applicant_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("applicant_Zh")
                        .HasComment("申请注册联系人（中文）");

                    b.Property<string>("blinding_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("blinding_En")
                        .HasComment("盲法（英文）");

                    b.Property<string>("blinding_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("blinding_Zh")
                        .HasComment("盲法（中文）");

                    b.Property<string>("calculated_results_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("calculated_results_En")
                        .HasComment("试验完成后的统计结果（英文）");

                    b.Property<string>("calculated_results_public")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("calculated_results_public")
                        .HasComment("是否公开试验完成后的统计结果");

                    b.Property<string>("calculated_results_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("calculated_results_Zh")
                        .HasComment("试验完成后的统计结果（中文）");

                    b.Property<string>("confounding_condition_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("confounding_condition_En")
                        .HasComment("容易混淆的疾病人群（英文）");

                    b.Property<string>("confounding_condition_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("confounding_condition_Zh")
                        .HasComment("容易混淆的疾病人群（中文）");

                    b.Property<string>("confounding_sample_size")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("confounding_sample_size")
                        .HasComment("容易混淆的疾病人群例数");

                    b.Property<string>("data_collection_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("data_collection_En")
                        .HasComment("数据采集和管理（英文）");

                    b.Property<string>("data_collection_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("data_collection_Zh")
                        .HasComment("数据采集和管理（中文）");

                    b.Property<string>("english_acronym_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("english_acronym_En")
                        .HasComment("注册题目简写英文");

                    b.Property<string>("english_acronym_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("english_acronym_Zh")
                        .HasComment("注册题目简写中文");

                    b.Property<string>("ethic_committee_address_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ethic_committee_address_En")
                        .HasComment("伦理委员会联系地址（英文）");

                    b.Property<string>("ethic_committee_address_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ethic_committee_address_Zh")
                        .HasComment("伦理委员会联系地址（中文）");

                    b.Property<string>("ethic_committee_approved")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ethic_committee_approved")
                        .HasComment("是否获伦理委员会批准");

                    b.Property<DateTime?>("ethic_committee_approved_date")
                        .HasColumnType("datetime2")
                        .HasColumnName("ethic_committee_approved_date")
                        .HasComment("伦理委员会批准日期");

                    b.Property<string>("ethic_committee_approved_no")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ethic_committee_approved_no")
                        .HasComment("伦理委员会批件文号");

                    b.Property<string>("ethic_committee_contact_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ethic_committee_contact_En")
                        .HasComment("伦理委员会联系人（英文）");

                    b.Property<string>("ethic_committee_contact_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ethic_committee_contact_Zh")
                        .HasComment("伦理委员会联系人（中文）");

                    b.Property<string>("ethic_committee_email")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ethic_committee_email")
                        .HasComment("伦理委员会联系人邮箱");

                    b.Property<string>("ethic_committee_name_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ethic_committee_name_En")
                        .HasComment("批准本研究的伦理委员会名称（英文）");

                    b.Property<string>("ethic_committee_name_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ethic_committee_name_Zh")
                        .HasComment("批准本研究的伦理委员会名称（中文）");

                    b.Property<string>("ethic_committee_phone")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ethic_committee_phone")
                        .HasComment("伦理委员会联系人电话");

                    b.Property<string>("exclusion_criteria_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("exclusion_criteria_En")
                        .HasComment("排除标准（英文）");

                    b.Property<string>("exclusion_criteria_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("exclusion_criteria_Zh")
                        .HasComment("排除标准（中文）");

                    b.Property<string>("follow_up_length")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("follow_up_length")
                        .HasComment("随访时间");

                    b.Property<string>("follow_up_unit")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("follow_up_unit")
                        .HasComment("随访时间单位");

                    b.Property<string>("funding_source_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("funding_source_En")
                        .HasComment("经费或物资来源（英文）");

                    b.Property<string>("funding_source_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("funding_source_Zh")
                        .HasComment("经费或物资来源（中文）");

                    b.Property<string>("gender")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("gender")
                        .HasComment("性别");

                    b.Property<string>("gold_standard_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("gold_standard_En")
                        .HasComment("金标准或参考标准（英文）");

                    b.Property<string>("gold_standard_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("gold_standard_Zh")
                        .HasComment("金标准或参考标准（中文）");

                    b.Property<string>("inclusion_criteria_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("inclusion_criteria_En")
                        .HasComment("纳入标准（英文）");

                    b.Property<string>("inclusion_criteria_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("inclusion_criteria_Zh")
                        .HasComment("纳入标准（中文）");

                    b.Property<string>("index_test_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("index_test_En")
                        .HasComment("指标试验（英文）");

                    b.Property<string>("index_test_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("index_test_Zh")
                        .HasComment("指标试验（中文）");

                    b.Property<string>("intervention_total_sample_size")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("intervention_total_sample_size")
                        .HasComment("干预措施样本总量");

                    b.Property<string>("ipd_sharing")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("ipd_sharing")
                        .HasComment("是否共享原始数据");

                    b.Property<string>("ipd_sharing_way_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ipd_sharing_way_En")
                        .HasComment("共享原始数据的方式（英文）");

                    b.Property<string>("ipd_sharing_way_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ipd_sharing_way_Zh")
                        .HasComment("共享原始数据的方式（中文）");

                    b.Property<string>("language")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("language")
                        .HasComment("语言");

                    b.Property<DateTime?>("mpa_approved_date")
                        .HasColumnType("datetime2")
                        .HasColumnName("mpa_approved_date")
                        .HasComment("国家药监局批准日期");

                    b.Property<string>("mpa_approved_no")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("mpa_approved_no")
                        .HasComment("国家药监局批准文号");

                    b.Property<string>("partner_registry_number")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("partner_registry_number")
                        .HasComment("在二级注册机构或其它机构的注册号");

                    b.Property<string>("primary_sponsor_address_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("primary_sponsor_address_En")
                        .HasComment("研究实施负责（组长）单位地址（英文）");

                    b.Property<string>("primary_sponsor_address_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("primary_sponsor_address_Zh")
                        .HasComment("研究实施负责（组长）单位地址（中文）");

                    b.Property<string>("primary_sponsor_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("primary_sponsor_En")
                        .HasComment("研究实施负责（组长）单位（英文）");

                    b.Property<string>("primary_sponsor_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("primary_sponsor_Zh")
                        .HasComment("研究实施负责（组长）单位（中文）");

                    b.Property<string>("publication_info_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("publication_info_En")
                        .HasComment("研究计划书或研究结果报告发表信息（英文）");

                    b.Property<string>("publication_info_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("publication_info_Zh")
                        .HasComment("研究计划书或研究结果报告发表信息（中文）");

                    b.Property<string>("publictitle_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("publictitle_En")
                        .HasComment("注册题目英文");

                    b.Property<string>("publictitle_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("publictitle_Zh")
                        .HasComment("注册题目中文");

                    b.Property<string>("randomization_procedure_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("randomization_procedure_En")
                        .HasComment("随机方法（请说明由何人用什么方法产生随机序列）（英文）");

                    b.Property<string>("randomization_procedure_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("randomization_procedure_Zh")
                        .HasComment("随机方法（请说明由何人用什么方法产生随机序列）（中文）");

                    b.Property<string>("recruiting_status")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("recruiting_status")
                        .HasComment("征募研究对象状况");

                    b.Property<DateTime?>("recruiting_time_end")
                        .HasMaxLength(500)
                        .HasColumnType("datetime2")
                        .HasColumnName("recruiting_time_end")
                        .HasComment("征募观察对象时间结束");

                    b.Property<DateTime?>("recruiting_time_start")
                        .HasMaxLength(500)
                        .HasColumnType("datetime2")
                        .HasColumnName("recruiting_time_start")
                        .HasComment("征募观察对象时间开始");

                    b.Property<string>("registration_number")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("registration_number")
                        .HasComment("注册号");

                    b.Property<string>("registration_status")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("registration_status")
                        .HasComment("注册号状态");

                    b.Property<string>("safety_committee")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("safety_committee")
                        .HasComment("数据与安全监察委员会");

                    b.Property<string>("scientific_title_acronym_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("scientific_title_acronym_En")
                        .HasComment("研究课题的正式科学名称简写（英文）");

                    b.Property<string>("scientific_title_acronym_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("scientific_title_acronym_Zh")
                        .HasComment("研究课题的正式科学名称简写（中文）");

                    b.Property<string>("scientific_title_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("scientific_title_En")
                        .HasComment("研究课题的正式科学名称英文");

                    b.Property<string>("scientific_title_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("scientific_title_Zh")
                        .HasComment("研究课题的正式科学名称中文");

                    b.Property<string>("sign_informed_consent")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("sign_informed_consent")
                        .HasComment("研究对象是否签署知情同意书");

                    b.Property<string>("statistical_methods_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("statistical_methods_En")
                        .HasComment("统计方法名称（英文）");

                    b.Property<string>("statistical_methods_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("statistical_methods_Zh")
                        .HasComment("统计方法名称（中文）");

                    b.Property<string>("study_design")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_design")
                        .HasComment("研究设计");

                    b.Property<string>("study_leader_address_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_leader_address_En")
                        .HasComment("研究负责人通讯地址（英文）");

                    b.Property<string>("study_leader_address_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_leader_address_Zh")
                        .HasComment("研究负责人通讯地址（中文）");

                    b.Property<string>("study_leader_affiliation_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_leader_affiliation_En")
                        .HasComment("研究负责人所在单位（英文）");

                    b.Property<string>("study_leader_affiliation_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_leader_affiliation_Zh")
                        .HasComment("研究负责人所在单位（中文）");

                    b.Property<string>("study_leader_email")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_leader_email")
                        .HasComment("研究负责人电子邮件");

                    b.Property<string>("study_leader_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_leader_En")
                        .HasComment("研究负责人（英文）");

                    b.Property<string>("study_leader_fax")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_leader_fax")
                        .HasComment("研究负责人传真");

                    b.Property<string>("study_leader_postcode")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_leader_postcode")
                        .HasComment("研究负责人邮政编码");

                    b.Property<string>("study_leader_telephone")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_leader_telephone")
                        .HasComment("研究负责人电话");

                    b.Property<string>("study_leader_website")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_leader_website")
                        .HasComment("研究负责人网址(自愿提供)");

                    b.Property<string>("study_leader_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_leader_Zh")
                        .HasComment("研究负责人（中文）");

                    b.Property<string>("study_objectives_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_objectives_En")
                        .HasComment("研究目的（英文）");

                    b.Property<string>("study_objectives_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_objectives_Zh")
                        .HasComment("研究目的（中文）");

                    b.Property<string>("study_phase")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_phase")
                        .HasComment("研究所处阶段");

                    b.Property<string>("study_subject_id")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_subject_id")
                        .HasComment("研究课题代号(代码)");

                    b.Property<DateTime?>("study_time_end")
                        .HasMaxLength(500)
                        .HasColumnType("datetime2")
                        .HasColumnName("study_time_end")
                        .HasComment("研究实施时间结束");

                    b.Property<DateTime?>("study_time_start")
                        .HasMaxLength(500)
                        .HasColumnType("datetime2")
                        .HasColumnName("study_time_start")
                        .HasComment("研究实施时间开始");

                    b.Property<string>("study_type")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("study_type")
                        .HasComment("研究类型");

                    b.Property<string>("target_condition_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("target_condition_En")
                        .HasComment("目标人群（英文）");

                    b.Property<string>("target_condition_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("target_condition_Zh")
                        .HasComment("目标人群（中文）");

                    b.Property<string>("target_disease_code")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("target_disease_code")
                        .HasComment("研究疾病代码");

                    b.Property<string>("target_disease_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("target_disease_En")
                        .HasComment("研究疾病（英文）");

                    b.Property<string>("target_disease_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("target_disease_Zh")
                        .HasComment("研究疾病（中文）");

                    b.Property<string>("target_sample_size")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("target_sample_size")
                        .HasComment("目标人群例数");

                    b.Property<string>("treatment_description_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("treatment_description_En")
                        .HasComment("药物成份或治疗方案详述（英文）");

                    b.Property<string>("treatment_description_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("treatment_description_Zh")
                        .HasComment("药物成份或治疗方案详述（中文）");

                    b.Property<string>("unblinding_rules_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("unblinding_rules_En")
                        .HasComment("揭盲或破盲原则和方法（英文）");

                    b.Property<string>("unblinding_rules_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("unblinding_rules_Zh")
                        .HasComment("揭盲或破盲原则和方法（中文）");

                    b.Property<string>("utn")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("utn")
                        .HasComment("全球唯一识别码");

                    b.HasKey("Key");

                    b.HasIndex("BusinessId")
                        .HasDatabaseName("IX_ProjectHistory_BusinessId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("FormCode")
                        .HasDatabaseName("IX_ProjectHistory_FormCode")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("Version")
                        .HasDatabaseName("IX_ProjectHistory_Version")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("projects_history", "i", t =>
                        {
                            t.HasComment("项目信息");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Itmctr.Domain.Entities.ProjectHumanSampleEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("BusinessId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("business_id")
                        .HasComment("关联业务id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<long>("ProjectId")
                        .HasColumnType("bigint")
                        .HasColumnName("project_id")
                        .HasComment("项目ID");

                    b.Property<int?>("RowIndex")
                        .HasColumnType("int")
                        .HasColumnName("row_index")
                        .HasComment("排序");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("version")
                        .HasComment("版本");

                    b.Property<string>("fate_of_sample")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("fate_of_sample")
                        .HasComment("人体标本去向");

                    b.Property<string>("sample_name_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("sample_name_En")
                        .HasComment("标本中文名（英文）");

                    b.Property<string>("sample_name_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("sample_name_Zh")
                        .HasComment("标本中文名（中文）");

                    b.Property<string>("sample_note_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("sample_note_En")
                        .HasComment("说明（英文）");

                    b.Property<string>("sample_note_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("sample_note_Zh")
                        .HasComment("说明（中文）");

                    b.Property<string>("tissue_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("tissue_En")
                        .HasComment("组织（英文）");

                    b.Property<string>("tissue_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("tissue_Zh")
                        .HasComment("组织（中文）");

                    b.HasKey("Key");

                    b.HasIndex("BusinessId")
                        .HasDatabaseName("IX_ProjectHumanSample_BusinessId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("ProjectId")
                        .HasDatabaseName("IX_ProjectHumanSample_ProjectId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("RowIndex")
                        .HasDatabaseName("IX_ProjectHumanSample_RowIndex")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("Version")
                        .HasDatabaseName("IX_ProjectHumanSample_Version")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("project_humansample", "i", t =>
                        {
                            t.HasComment("采集人体标本历史记录");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Itmctr.Domain.Entities.ProjectHumanSampleHistoryEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("BusinessId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("business_id")
                        .HasComment("关联业务id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<long>("ProjectId")
                        .HasColumnType("bigint")
                        .HasColumnName("project_id")
                        .HasComment("项目ID");

                    b.Property<int?>("RowIndex")
                        .HasColumnType("int")
                        .HasColumnName("row_index")
                        .HasComment("排序");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("version")
                        .HasComment("版本");

                    b.Property<string>("fate_of_sample")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("fate_of_sample")
                        .HasComment("人体标本去向");

                    b.Property<string>("sample_name_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("sample_name_En")
                        .HasComment("标本中文名（英文）");

                    b.Property<string>("sample_name_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("sample_name_Zh")
                        .HasComment("标本中文名（中文）");

                    b.Property<string>("sample_note_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("sample_note_En")
                        .HasComment("说明（英文）");

                    b.Property<string>("sample_note_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("sample_note_Zh")
                        .HasComment("说明（中文）");

                    b.Property<string>("tissue_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("tissue_En")
                        .HasComment("组织（英文）");

                    b.Property<string>("tissue_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("tissue_Zh")
                        .HasComment("组织（中文）");

                    b.HasKey("Key");

                    b.HasIndex("BusinessId")
                        .HasDatabaseName("IX_ProjectHumanSampleHistory_BusinessId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("ProjectId")
                        .HasDatabaseName("IX_ProjectHumanSampleHistory_ProjectId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("RowIndex")
                        .HasDatabaseName("IX_ProjectHumanSampleHistory_RowIndex")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("Version")
                        .HasDatabaseName("IX_ProjectHumanSampleHistory_Version")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("project_humansample_history", "i", t =>
                        {
                            t.HasComment("采集人体标本历史记录");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Itmctr.Domain.Entities.ProjectInterventionEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("BusinessId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("business_id")
                        .HasComment("关联业务id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<long>("ProjectId")
                        .HasColumnType("bigint")
                        .HasColumnName("project_id")
                        .HasComment("项目ID");

                    b.Property<int?>("RowIndex")
                        .HasColumnType("int")
                        .HasColumnName("row_index")
                        .HasComment("排序");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("version")
                        .HasComment("版本");

                    b.Property<string>("intervention_code")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("intervention_code")
                        .HasComment("干预措施代码");

                    b.Property<string>("intervention_group_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("intervention_group_En")
                        .HasComment("组别（英文）");

                    b.Property<string>("intervention_group_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("intervention_group_Zh")
                        .HasComment("组别（中文）");

                    b.Property<string>("intervention_name_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("intervention_name_En")
                        .HasComment("干预措施（英文）");

                    b.Property<string>("intervention_name_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("intervention_name_Zh")
                        .HasComment("干预措施（中文）");

                    b.Property<string>("intervention_sample_size")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("intervention_sample_size")
                        .HasComment("样本量");

                    b.HasKey("Key");

                    b.HasIndex("BusinessId")
                        .HasDatabaseName("IX_ProjectIntervention_BusinessId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("ProjectId")
                        .HasDatabaseName("IX_ProjectIntervention_ProjectId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("RowIndex")
                        .HasDatabaseName("IX_ProjectIntervention_RowIndex")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("Version")
                        .HasDatabaseName("IX_ProjectIntervention_Version")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("project_intervention", "i", t =>
                        {
                            t.HasComment("干预措施");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Itmctr.Domain.Entities.ProjectInterventionHistoryEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("BusinessId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("business_id")
                        .HasComment("关联业务id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<long>("ProjectId")
                        .HasColumnType("bigint")
                        .HasColumnName("project_id")
                        .HasComment("项目ID");

                    b.Property<int?>("RowIndex")
                        .HasColumnType("int")
                        .HasColumnName("row_index")
                        .HasComment("排序");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("version")
                        .HasComment("版本");

                    b.Property<string>("intervention_code")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("intervention_code")
                        .HasComment("干预措施代码");

                    b.Property<string>("intervention_group_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("intervention_group_En")
                        .HasComment("组别（英文）");

                    b.Property<string>("intervention_group_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("intervention_group_Zh")
                        .HasComment("组别（中文）");

                    b.Property<string>("intervention_name_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("intervention_name_En")
                        .HasComment("干预措施（英文）");

                    b.Property<string>("intervention_name_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("intervention_name_Zh")
                        .HasComment("干预措施（中文）");

                    b.Property<string>("intervention_sample_size")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("intervention_sample_size")
                        .HasComment("样本量");

                    b.HasKey("Key");

                    b.HasIndex("BusinessId")
                        .HasDatabaseName("IX_ProjectInterventionHistory_BusinessId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("ProjectId")
                        .HasDatabaseName("IX_ProjectInterventionHistory_ProjectId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("RowIndex")
                        .HasDatabaseName("IX_ProjectInterventionHistory_RowIndex")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("Version")
                        .HasDatabaseName("IX_ProjectInterventionHistory_Version")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("project_intervention_history", "i", t =>
                        {
                            t.HasComment("干预措施历史记录");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Itmctr.Domain.Entities.ProjectMeasurementEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("BusinessId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("business_id")
                        .HasComment("关联业务id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<long>("ProjectId")
                        .HasColumnType("bigint")
                        .HasColumnName("project_id")
                        .HasComment("项目ID");

                    b.Property<int?>("RowIndex")
                        .HasColumnType("int")
                        .HasColumnName("row_index")
                        .HasComment("排序");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("version")
                        .HasComment("版本");

                    b.Property<string>("measure_method_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("measure_method_En")
                        .HasComment("测量方法（英文）");

                    b.Property<string>("measure_method_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("measure_method_Zh")
                        .HasComment("测量方法（中文）");

                    b.Property<string>("measure_time_point_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("measure_time_point_En")
                        .HasComment("测量时间点（英文）");

                    b.Property<string>("measure_time_point_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("measure_time_point_Zh")
                        .HasComment("测量时间点（中文）");

                    b.Property<string>("outcome_name_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("outcome_name_En")
                        .HasComment("指标中文名（英文）");

                    b.Property<string>("outcome_name_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("outcome_name_Zh")
                        .HasComment("指标中文名（中文）");

                    b.Property<string>("outcome_type")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("outcome_type")
                        .HasComment("指标类型");

                    b.HasKey("Key");

                    b.HasIndex("BusinessId")
                        .HasDatabaseName("IX_ProjectMeasurement_BusinessId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("ProjectId")
                        .HasDatabaseName("IX_ProjectMeasurement_ProjectId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("RowIndex")
                        .HasDatabaseName("IX_ProjectMeasurement_RowIndex")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("Version")
                        .HasDatabaseName("IX_ProjectMeasurement_Version")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("project_measurement", "i", t =>
                        {
                            t.HasComment("测量指标");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Itmctr.Domain.Entities.ProjectMeasurementHistoryEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("BusinessId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("business_id")
                        .HasComment("关联业务id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<long>("ProjectId")
                        .HasColumnType("bigint")
                        .HasColumnName("project_id")
                        .HasComment("项目ID");

                    b.Property<int?>("RowIndex")
                        .HasColumnType("int")
                        .HasColumnName("row_index")
                        .HasComment("排序");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("version")
                        .HasComment("版本");

                    b.Property<string>("measure_method_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("measure_method_En")
                        .HasComment("测量方法（英文）");

                    b.Property<string>("measure_method_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("measure_method_Zh")
                        .HasComment("测量方法（中文）");

                    b.Property<string>("measure_time_point_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("measure_time_point_En")
                        .HasComment("测量时间点（英文）");

                    b.Property<string>("measure_time_point_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("measure_time_point_Zh")
                        .HasComment("测量时间点（中文）");

                    b.Property<string>("outcome_name_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("outcome_name_En")
                        .HasComment("指标中文名（英文）");

                    b.Property<string>("outcome_name_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("outcome_name_Zh")
                        .HasComment("指标中文名（中文）");

                    b.Property<string>("outcome_type")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("outcome_type")
                        .HasComment("指标类型");

                    b.HasKey("Key");

                    b.HasIndex("BusinessId")
                        .HasDatabaseName("IX_ProjectMeasurementHistory_BusinessId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("ProjectId")
                        .HasDatabaseName("IX_ProjectMeasurementHistory_ProjectId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("RowIndex")
                        .HasDatabaseName("IX_ProjectMeasurementHistory_RowIndex")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("Version")
                        .HasDatabaseName("IX_ProjectMeasurementHistory_Version")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("project_measurement_history", "i", t =>
                        {
                            t.HasComment("测量指标历史记录");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Itmctr.Domain.Entities.ProjectResearchSiteEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("BusinessId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("business_id")
                        .HasComment("关联业务id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<long>("ProjectId")
                        .HasColumnType("bigint")
                        .HasColumnName("project_id")
                        .HasComment("项目ID");

                    b.Property<int?>("RowIndex")
                        .HasColumnType("int")
                        .HasColumnName("row_index")
                        .HasComment("排序");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("version")
                        .HasComment("版本");

                    b.Property<string>("site_city_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("site_city_En")
                        .HasComment("市(区县)（英文）");

                    b.Property<string>("site_city_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("site_city_Zh")
                        .HasComment("市(区县)（中文）");

                    b.Property<string>("site_country_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("site_country_En")
                        .HasComment("国家（英文）");

                    b.Property<string>("site_country_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("site_country_Zh")
                        .HasComment("国家（中文）");

                    b.Property<string>("site_institution_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("site_institution_En")
                        .HasComment("单位(医院)（英文）");

                    b.Property<string>("site_institution_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("site_institution_Zh")
                        .HasComment("单位(医院)（中文）");

                    b.Property<string>("site_level_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("site_level_En")
                        .HasComment("单位级别（英文）");

                    b.Property<string>("site_level_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("site_level_Zh")
                        .HasComment("单位级别（中文）");

                    b.Property<string>("site_province_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("site_province_En")
                        .HasComment("省(直辖市)（英文）");

                    b.Property<string>("site_province_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("site_province_Zh")
                        .HasComment("省(直辖市)（中文）");

                    b.HasKey("Key");

                    b.HasIndex("BusinessId")
                        .HasDatabaseName("IX_ProjectResearchSite_BusinessId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("ProjectId")
                        .HasDatabaseName("IX_ProjectResearchSite_ProjectId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("RowIndex")
                        .HasDatabaseName("IX_ProjectResearchSite_RowIndex")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("Version")
                        .HasDatabaseName("IX_ProjectResearchSite_Version")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("project_research_site", "i", t =>
                        {
                            t.HasComment("研究实施地点");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Itmctr.Domain.Entities.ProjectResearchSiteHistoryEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("BusinessId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("business_id")
                        .HasComment("关联业务id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<long>("ProjectId")
                        .HasColumnType("bigint")
                        .HasColumnName("project_id")
                        .HasComment("项目ID");

                    b.Property<int?>("RowIndex")
                        .HasColumnType("int")
                        .HasColumnName("row_index")
                        .HasComment("排序");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("version")
                        .HasComment("版本");

                    b.Property<string>("site_city_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("site_city_En")
                        .HasComment("市(区县)（英文）");

                    b.Property<string>("site_city_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("site_city_Zh")
                        .HasComment("市(区县)（中文）");

                    b.Property<string>("site_country_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("site_country_En")
                        .HasComment("国家（英文）");

                    b.Property<string>("site_country_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("site_country_Zh")
                        .HasComment("国家（中文）");

                    b.Property<string>("site_institution_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("site_institution_En")
                        .HasComment("单位(医院)（英文）");

                    b.Property<string>("site_institution_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("site_institution_Zh")
                        .HasComment("单位(医院)（中文）");

                    b.Property<string>("site_level_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("site_level_En")
                        .HasComment("单位级别（英文）");

                    b.Property<string>("site_level_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("site_level_Zh")
                        .HasComment("单位级别（中文）");

                    b.Property<string>("site_province_en")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("site_province_En")
                        .HasComment("省(直辖市)（英文）");

                    b.Property<string>("site_province_zh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("site_province_Zh")
                        .HasComment("省(直辖市)（中文）");

                    b.HasKey("Key");

                    b.HasIndex("BusinessId")
                        .HasDatabaseName("IX_ProjectResearchSiteHistory_BusinessId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("ProjectId")
                        .HasDatabaseName("IX_ProjectResearchSiteHistory_ProjectId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("RowIndex")
                        .HasDatabaseName("IX_ProjectResearchSiteHistory_RowIndex")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("Version")
                        .HasDatabaseName("IX_ProjectResearchSiteHistory_Version")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("project_research_site_history", "i", t =>
                        {
                            t.HasComment("研究实施地点历史记录");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Itmctr.Domain.Entities.ProjectSponsorEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("BusinessId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("business_id")
                        .HasComment("关联业务id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<long>("ProjectId")
                        .HasColumnType("bigint")
                        .HasColumnName("project_id")
                        .HasComment("项目ID");

                    b.Property<int?>("RowIndex")
                        .HasColumnType("int")
                        .HasColumnName("row_index")
                        .HasComment("排序");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("version")
                        .HasComment("版本");

                    b.Property<string>("sponsor_address_en")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("sponsor_address_En")
                        .HasComment("具体地址（英文）");

                    b.Property<string>("sponsor_address_zh")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("sponsor_address_Zh")
                        .HasComment("具体地址（中文）");

                    b.Property<string>("sponsor_city_en")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("sponsor_city_En")
                        .HasComment("市(区县)（英文）");

                    b.Property<string>("sponsor_city_zh")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("sponsor_city_Zh")
                        .HasComment("市(区县)（中文）");

                    b.Property<string>("sponsor_country_en")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("sponsor_country_En")
                        .HasComment("国家（英文）");

                    b.Property<string>("sponsor_country_zh")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("sponsor_country_Zh")
                        .HasComment("国家（中文）");

                    b.Property<string>("sponsor_institution_en")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("sponsor_institution_En")
                        .HasComment("单位（英文）");

                    b.Property<string>("sponsor_institution_zh")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("sponsor_institution_Zh")
                        .HasComment("单位（中文）");

                    b.Property<string>("sponsor_province_en")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("sponsor_province_En")
                        .HasComment("省(直辖市)（英文）");

                    b.Property<string>("sponsor_province_zh")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("sponsor_province_Zh")
                        .HasComment("省(直辖市)（中文）");

                    b.HasKey("Key");

                    b.HasIndex("BusinessId")
                        .HasDatabaseName("IX_ProjectSponsor_BusinessId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("ProjectId")
                        .HasDatabaseName("IX_ProjectSponsor_ProjectId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("RowIndex")
                        .HasDatabaseName("IX_ProjectSponsor_RowIndex")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("Version")
                        .HasDatabaseName("IX_ProjectSponsor_Version")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("project_sponsor", "i", t =>
                        {
                            t.HasComment("试验主办单位");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Itmctr.Domain.Entities.ProjectSponsorHistoryEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("BusinessId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("business_id")
                        .HasComment("关联业务id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<long>("ProjectId")
                        .HasColumnType("bigint")
                        .HasColumnName("project_id")
                        .HasComment("项目ID");

                    b.Property<int?>("RowIndex")
                        .HasColumnType("int")
                        .HasColumnName("row_index")
                        .HasComment("排序");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("version")
                        .HasComment("版本");

                    b.Property<string>("sponsor_address_en")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("sponsor_address_En")
                        .HasComment("具体地址（英文）");

                    b.Property<string>("sponsor_address_zh")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("sponsor_address_Zh")
                        .HasComment("具体地址（中文）");

                    b.Property<string>("sponsor_city_en")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("sponsor_city_En")
                        .HasComment("市(区县)（英文）");

                    b.Property<string>("sponsor_city_zh")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("sponsor_city_Zh")
                        .HasComment("市(区县)（中文）");

                    b.Property<string>("sponsor_country_en")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("sponsor_country_En")
                        .HasComment("国家（英文）");

                    b.Property<string>("sponsor_country_zh")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("sponsor_country_Zh")
                        .HasComment("国家（中文）");

                    b.Property<string>("sponsor_institution_en")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("sponsor_institution_En")
                        .HasComment("单位（英文）");

                    b.Property<string>("sponsor_institution_zh")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("sponsor_institution_Zh")
                        .HasComment("单位（中文）");

                    b.Property<string>("sponsor_province_en")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("sponsor_province_En")
                        .HasComment("省(直辖市)（英文）");

                    b.Property<string>("sponsor_province_zh")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("sponsor_province_Zh")
                        .HasComment("省(直辖市)（中文）");

                    b.HasKey("Key");

                    b.HasIndex("BusinessId")
                        .HasDatabaseName("IX_ProjectSponsorHistory_BusinessId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("ProjectId")
                        .HasDatabaseName("IX_ProjectSponsorHistory_ProjectId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("RowIndex")
                        .HasDatabaseName("IX_ProjectSponsorHistory_RowIndex")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("Version")
                        .HasDatabaseName("IX_ProjectSponsorHistory_Version")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("project_sponsor_history", "i", t =>
                        {
                            t.HasComment("试验主办单位历史记录");
                        });
                });
#pragma warning restore 612, 618
        }
    }
}
