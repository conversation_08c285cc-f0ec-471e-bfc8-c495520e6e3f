using XJ.Framework.Itmctr.Domain.Entities;
using XJ.Framework.Itmctr.Domain.Repositories.Interfaces;
using XJ.Framework.Itmctr.EntityFrameworkCore;

namespace XJ.Framework.Rbac.EntityFrameworkCore.Repositories;

/// <summary>  
/// AsyncTask 仓储实现  
/// </summary>  
public class ProjectHumanSampleHistoryRepository : BaseAuditRepository<ItmctrDbContext, long, ProjectHumanSampleHistoryEntity>,
   IProjectHumanSampleHistoryRepository
{
    public ProjectHumanSampleHistoryRepository(IServiceProvider serviceProvider) : base(
        serviceProvider)
    {
    }
}
