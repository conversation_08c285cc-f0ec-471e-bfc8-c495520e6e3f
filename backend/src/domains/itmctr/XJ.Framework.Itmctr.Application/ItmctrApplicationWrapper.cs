using XJ.Framework.Files.ApiClient;
using XJ.Framework.Itmctr.Application.Contract.Interfaces;
using XJ.Framework.Itmctr.Application.Options;
using XJ.Framework.Itmctr.Application.Services;
using XJ.Framework.Rbac.ApiClient;

namespace XJ.Framework.Itmctr.Application;

public class ItmctrApplicationWrapper : ApplicationWrapper
{
    public override void Init(IServiceCollection services, IConfigurationRoot configuration)
    {
        // services.AddSingleton<YourService>();

        services.AddHttpClient<UserApiClient>();
        services.AddHttpClient<FilesApiClient>();
        services.AddHttpClient<DynamicFormApiClient>();
        services.AddHttpClient<DynamicFormMgtApiClient>();
        services.AddHttpClient<UserMgtApiClient>();

        services.AddHttpClient("UnicomApi")
            .AddHttpMessageHandler<UnicomTokenHandler>();

        services.AddSingleton<ITokenProvider, TokenProvider>();
        services.AddTransient<UnicomTokenHandler>();
        services.AddTransient<UnicomApiClient>();

        services.AddTransient<IFormRecognitionService, FormRecognitionService>();

        services.Configure<UnicomOption>(configuration.GetSection("UnicomOptions"));
    }
}