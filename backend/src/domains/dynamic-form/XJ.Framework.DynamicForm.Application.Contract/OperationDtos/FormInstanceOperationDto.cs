using XJ.Framework.DynamicForm.Domain.Shared.Enums;

namespace XJ.Framework.DynamicForm.Application.Contract.OperationDtos;

/// <summary>
/// FormInstance 操作 DTO
/// </summary>
public class FormInstanceOperationDto : BaseOperationDto
{
    public string? Language { get; set; }

    /// <summary>
    /// 表单code
    /// </summary>
    public required string FormCode { get; set; }

    /// <summary>
    /// 表单版本
    /// </summary>
    public required string FormVersion { get; set; }

    /// <summary>
    /// 关联业务id
    /// </summary>
    public required string BusinessId { get; set; }

    /// <summary>
    /// 版本号
    /// </summary>
    public required string Version { get; set; }

    /// <summary>
    /// 前一个版本的表单实例id
    /// </summary>
    public long? PreviousInstanceId { get; set; }

    /// <summary>
    /// 版本创建时间
    /// </summary>
    public required DateTimeOffset VersionTime { get; set; }

    /// <summary>
    /// 前一个版本的版本号
    /// </summary>
    public string? PreviousVersion { get; set; }

    /// <summary>
    /// 表单状态 1- 草稿 2- 已提交 3- 已确认 4- 已作废 5- 已驳回
    /// </summary>
    public FormInstanceStatus Status { get; set; }

    /// <summary>
    /// 发起人
    /// </summary>
    public long ApplyUserId { get; set; }

    /// <summary>
    /// 是否已失效
    /// </summary>
    public bool IsObsoleted { get; set; }
}