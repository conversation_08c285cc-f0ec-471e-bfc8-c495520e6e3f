using XJ.Framework.Library.Common.Abstraction.JsonConverters;
using XJ.Framework.Library.Domain.Shared.Dtos;

namespace XJ.Framework.DynamicForm.Application.Contract.Interfaces;

/// <summary>
/// FormInstance 服务接口
/// </summary>
public interface IFormInstanceService :
    IAppService<long, FormInstanceDto, FormInstanceQueryCriteria>,
    IEditableAppService<long, FormInstanceOperationDto>
{
    Task<string> CreateFormInstanceAsync(string formCode, string businessId);
    Task<FormDefinitionDto> GetNewestFormInstanceAsync(string businessId, string version);
    Task<FormDefinitionDto> GetNewestFormInstanceAsync(string businessId);
    Task<FormDefinitionDto> GetNamedVersionFormInstanceAsync(string businessId, string version);
    Task<FormInstanceDto> GetNewestFormInstanceDtoAsync(string businessId);
    Task<FormInstanceDto> GetNamedVersionFormInstanceDtoAsync(string businessId, string version);
    Task<string> SaveInstanceAsync(string businessId, string version, FormDefinitionDto formDefinitionDto);
    Task<string> SaveInstanceAsync(string businessId, FormDefinitionDto formDefinitionDto);
    Task<NewFormInstanceDto> SaveInstanceWithoutVersionAsync(string formCode, FormDefinitionDto formDefinitionDto);
    Task<List<FormInstanceDto>> GetFormInstanceVersionsAsync(string businessId);

    Task<FormDefinitionDto> GetPreviousDifferenceAsync(string businessId,
        string version);

    Task<FormDefinitionDto> GetNamedVersionDifferenceAsync(string businessId,
        string version, string namedVersion);

    Task<FormInstanceDto?> GetNamedInstanceAsync(string businessId, string version);
    Task<string> CreateNewestFormInstanceAsync(string formCode, string businessId);
    Task<bool> ConfirmInstanceAsync(string businessId, string version);
    Task<bool> ConfirmInstanceAsync(string businessId);
    Task<bool> CancelInstanceAsync(string businessId, string version);
    Task<bool> SubmitInstanceAsync(string businessId, string version);
    Task<bool> SubmitInstanceAsync(string businessId);
    Task<List<string>> GetValidateAsync(string businessId, string version);
    Task<List<string>> GetValidateAsync(string businessId);


    Task<FormInstancePageDto> GetUserPageAsync(
        PagedQueryCriteria<FormInstanceQueryCriteria> criteria);

    new Task<FormInstancePageDto> GetPageAsync(
        PagedQueryCriteria<FormInstanceQueryCriteria> criteria);

    new Task<FormInstancePageDto> GetNewestPageAsync(
        PagedQueryCriteria<FormInstanceQueryCriteria> criteria);

    Task<bool> RejectInstanceAsync(string businessId, string version,
        Dictionary<string, AnnotationValue?>? annotationValues);

    Task<bool> RejectInstanceAsync(FormInstanceDto formInstanceDto,
        Dictionary<string, AnnotationValue?>? annotationValues);

    Task<bool> SaveAsync(string businessId, string version,
        FormDefinitionDto formDefinitionDto);

    Task<bool> SaveAsync(string businessId,
        FormDefinitionDto formDefinitionDto);

    Task<int> GetProjectCountAsync(string formCode, int status, List<FormDataDynamicQuery> query);
}
