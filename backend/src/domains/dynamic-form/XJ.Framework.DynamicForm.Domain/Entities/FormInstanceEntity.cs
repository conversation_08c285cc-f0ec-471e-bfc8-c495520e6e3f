using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using XJ.Framework.DynamicForm.Domain.Shared.Enums;
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.DynamicForm.Domain.Entities;

/// <summary>
/// FormInstance 实体
/// </summary>
[Table("form_instances", Schema = "d")]
[SoftDeleteIndex("UX_form_instances_version_business_id_not_deleted", nameof(Version), nameof(BusinessId),
    IsUnique = true)]
[SoftDeleteIndex("IX_form_instances_business_id_not_deleted", nameof(BusinessId))]
public class FormInstanceEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 填写语言
    /// </summary>
    [Column("language")]
    public string? Language { get; set; }

    /// <summary>
    /// 表单code
    /// </summary>
    [Column("form_code")]
    public required string FormCode { get; set; }

    /// <summary>
    /// 表单版本
    /// </summary>
    [Column("form_version")]
    public required string FormVersion { get; set; }

    /// <summary>
    /// 关联业务id
    /// </summary>
    [Column("business_id")]
    public required string BusinessId { get; set; }

    /// <summary>
    /// 版本号
    /// </summary>
    [Column("version")]
    public required string Version { get; set; }

    /// <summary>
    /// 前一个版本的表单实例id
    /// </summary>
    [Column("previous_instance_id")]
    public long? PreviousInstanceId { get; set; }

    /// <summary>
    /// 版本创建时间
    /// </summary>
    [Column("version_time")]
    public required DateTimeOffset VersionTime { get; set; }

    /// <summary>
    /// 前一个版本的版本号
    /// </summary>
    [Column("previous_version")]
    public string? PreviousVersion { get; set; }

    /// <summary>
    /// 表单状态 1- 草稿 2- 已提交 3- 已确认 4- 已作废 5- 已驳回
    /// </summary>
    [Column("status")]
    public FormInstanceStatus Status { get; set; }

    /// <summary>
    /// 发起人
    /// </summary>
    [Column("apply_user_id")]
    public required long ApplyUserId { get; set; }

    /// <summary>
    /// 是否已失效
    /// </summary>
    [Column("obsoleted")]
    public bool IsObsoleted { get; set; }
}