using System.Linq.Expressions;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Library.Domain.Common;
using XJ.Framework.Library.EntityFrameworkCore.QueryCriteria;

namespace XJ.Framework.DynamicForm.Domain.Repositories.Interfaces;

/// <summary>
/// FormInstance 仓储接口
/// </summary>
public interface IFormInstanceRepository : IAuditRepository<long, FormInstanceEntity>
{
    Task<PageData<long, FormInstanceEntity>> GetNewestPageAsync(
        Expression<Func<FormInstanceEntity, bool>> whereLambda,
        Dictionary<string, string> dynamicQueries,
        List<FormDataDynamicQuery> formDataDynamicQueries,
        Dictionary<string, SortDirection> dynamicOrderBys,
        Dictionary<string, SortDirection> formDataDynamicOrderBys,
        int rowIndex,
        int pageSize,
        List<OrderbyDirection<FormInstanceEntity>> orderBy,
        bool isNoTracking = true);

    Task<PageData<long, FormInstanceEntity>> GetNewestPageRawSqlAsync(
        List<WhereItem> whereItems,
        Dictionary<string, string> dynamicQueries,
        List<FormDataDynamicQuery> formDataDynamicQueries,
        List<OrderByItem> orderByItems,
        Dictionary<string, SortDirection> dynamicOrderBys,
        Dictionary<string, SortDirection> formDataDynamicOrderBys,
        int rowIndex,
        int pageSize);

    Task<PageData<long, FormInstanceEntity>> GetPageAsync(
        Expression<Func<FormInstanceEntity, bool>> whereLambda,
        Dictionary<string, string> dynamicQueries,
        List<FormDataDynamicQuery> formDataDynamicQueries,
        int rowIndex,
        int pageSize,
        List<OrderbyDirection<FormInstanceEntity>> orderBy,
        bool isNoTracking = true);

    Task<int> GetCountAsync(Expression<Func<FormInstanceEntity, bool>> whereLambda,
        List<FormDataDynamicQuery> formDataDynamicQueries);
}
