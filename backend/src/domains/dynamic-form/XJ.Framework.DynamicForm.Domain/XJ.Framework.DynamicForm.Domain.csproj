<Project Sdk="Microsoft.NET.Sdk">

    <Import Project="..\..\..\..\Common.Secrets.props"/>
    <Import Project="..\..\..\..\Common.props"/>

    <ItemGroup>
        <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.Domain\XJ.Framework.Library.Domain.csproj"/>
        <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.EntityFrameworkCore\XJ.Framework.Library.EntityFrameworkCore.csproj" />
        <ProjectReference Include="..\XJ.Framework.DynamicForm.Domain.Shared\XJ.Framework.DynamicForm.Domain.Shared.csproj" />
    </ItemGroup>

</Project> 