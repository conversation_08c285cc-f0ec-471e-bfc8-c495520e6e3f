# 公用SQL构建方法重构总结

## 重构目标

将`GetNewestPageRawSqlAsync`方法中的countSql逻辑提取为公用方法，使`GetNewestCountRawSqlAsync`方法也能复用相同的SQL构建逻辑。

## 重构前的问题

### 1. 代码重复
- `GetNewestPageRawSqlAsync`中包含完整的SQL构建逻辑
- `GetNewestCountRawSqlAsync`方法为空，需要实现相同的逻辑
- 两个方法需要相同的WHERE条件构建逻辑

### 2. 维护困难
- SQL构建逻辑分散在多个方法中
- 修改查询逻辑需要同时修改多个地方
- 容易出现逻辑不一致的问题

## 重构后的架构

### 1. 新增公用方法：`BuildNewestQuerySql`

```csharp
private (string completeSql, Dictionary<string, object> parameters) BuildNewestQuerySql(
    List<WhereItem> whereItems,
    Dictionary<string, string> dynamicQueries,
    List<FormDataDynamicQuery> formDataDynamicQueries,
    List<OrderByItem> orderByItems,
    Dictionary<string, SortDirection> dynamicOrderBys,
    Dictionary<string, SortDirection> formDataDynamicOrderBys)
```

**功能**：
- 添加最新版本的基础条件（is_obsoleted = false, is_deleted = false, NOT EXISTS子查询）
- 处理动态查询条件
- 处理表单数据动态查询条件
- 处理动态排序条件
- 构建完整的SQL查询和参数字典

### 2. 重构后的方法结构

#### GetNewestPageRawSqlAsync（分页查询）
```csharp
public async Task<PageData<long, FormInstanceEntity>> GetNewestPageRawSqlAsync(...)
{
    // 1. 调用公用方法构建SQL
    var (completeSql, parameters) = BuildNewestQuerySql(...);
    
    // 2. 构建分页和计数SQL
    var countSql = $"SELECT COUNT(*) FROM ({completeSql}) AS subquery";
    var pageSql = $"{completeSql} OFFSET {rowIndex} ROWS FETCH NEXT {pageSize} ROWS ONLY";
    
    // 3. 执行查询
    var data = this.DbSet.FromSqlRaw(pageSql, parameters.Values.ToArray());
    var totalCount = await this.DbContext.Database.SqlQueryRaw<long>(countSql, parameters.Values.ToArray()).FirstOrDefaultAsync();
    
    // 4. 返回分页结果
    return new PageData<long, FormInstanceEntity> { Totals = totalCount, Rows = await data.ToListAsync() };
}
```

#### GetNewestCountRawSqlAsync（计数查询）
```csharp
public async Task<long> GetNewestCountRawSqlAsync(...)
{
    // 1. 准备空的排序条件（计数查询不需要排序）
    var emptyOrderByItems = new List<OrderByItem>();
    var emptyDynamicOrderBys = new Dictionary<string, SortDirection>();
    var emptyFormDataDynamicOrderBys = new Dictionary<string, SortDirection>();
    
    // 2. 调用公用方法构建SQL
    var (completeSql, parameters) = BuildNewestQuerySql(...);
    
    // 3. 构建计数SQL并执行
    var countSql = $"SELECT COUNT(*) FROM ({completeSql}) AS subquery";
    return await this.DbContext.Database.SqlQueryRaw<long>(countSql, parameters.Values.ToArray()).FirstOrDefaultAsync();
}
```

## 重构的关键改进

### 1. 代码复用
- ✅ 两个方法共享相同的SQL构建逻辑
- ✅ WHERE条件构建逻辑统一
- ✅ 参数处理逻辑统一

### 2. 维护性提升
- ✅ 修改查询逻辑只需要修改一个地方
- ✅ 逻辑一致性得到保证
- ✅ 代码结构更清晰

### 3. 性能优化
- ✅ 计数查询不包含不必要的ORDER BY子句
- ✅ 参数处理更高效（使用`parameters.Values.ToArray()`）

### 4. 类型安全
- ✅ `GetNewestCountRawSqlAsync`返回正确的`long`类型
- ✅ 使用`Database.SqlQueryRaw<long>`执行标量查询

## 重构前后对比

### 重构前
```csharp
// GetNewestPageRawSqlAsync - 包含完整逻辑（约45行）
public async Task<PageData<long, FormInstanceEntity>> GetNewestPageRawSqlAsync(...)
{
    whereItems.Add(WhereItemFactory.Equal("is_obsoleted", false));
    whereItems.Add(WhereItemFactory.Equal("is_deleted", false));
    whereItems.Add(WhereItemFactory.NotExists(...));
    AddDynamicWhereItems(whereItems, dynamicQueries);
    AddFormDataDynamicWhereItems(whereItems, formDataDynamicQueries);
    AddDynamicOrderByItems(orderByItems, dynamicOrderBys);
    AddFormDataDynamicOrderByItems(orderByItems, formDataDynamicOrderBys);
    // ... 构建SQL和执行查询
}

// GetNewestCountRawSqlAsync - 空方法
public async Task<long> GetNewestCountRawSqlAsync(...)
{
    // 空实现
}
```

### 重构后
```csharp
// BuildNewestQuerySql - 公用SQL构建逻辑（约20行）
private (string, Dictionary<string, object>) BuildNewestQuerySql(...) { ... }

// GetNewestPageRawSqlAsync - 简化为调用公用方法（约15行）
public async Task<PageData<long, FormInstanceEntity>> GetNewestPageRawSqlAsync(...)
{
    var (completeSql, parameters) = BuildNewestQuerySql(...);
    // 构建分页SQL并执行
}

// GetNewestCountRawSqlAsync - 完整实现（约10行）
public async Task<long> GetNewestCountRawSqlAsync(...)
{
    var (completeSql, parameters) = BuildNewestQuerySql(...);
    // 构建计数SQL并执行
}
```

## 使用示例

### 分页查询
```csharp
var pageData = await repository.GetNewestPageRawSqlAsync(
    whereItems, dynamicQueries, formDataDynamicQueries,
    orderByItems, dynamicOrderBys, formDataDynamicOrderBys,
    rowIndex: 0, pageSize: 10);

Console.WriteLine($"总数: {pageData.Totals}");
Console.WriteLine($"当前页数据: {pageData.Rows.Count}");
```

### 仅获取总数
```csharp
var totalCount = await repository.GetNewestCountRawSqlAsync(
    whereItems, dynamicQueries, formDataDynamicQueries);

Console.WriteLine($"符合条件的记录总数: {totalCount}");
```

## 构建状态

- ✅ 编译成功（0错误，0警告）
- ✅ 代码重复消除
- ✅ 逻辑一致性保证
- ✅ 类型安全改进
- ✅ 性能优化完成

## 总结

这次重构成功地：

1. **消除了代码重复**：将SQL构建逻辑提取为公用方法
2. **提高了维护性**：修改查询逻辑只需要修改一个地方
3. **完善了功能**：实现了之前空的`GetNewestCountRawSqlAsync`方法
4. **优化了性能**：计数查询不包含不必要的ORDER BY子句
5. **保证了一致性**：两个方法使用完全相同的WHERE条件逻辑

重构后的代码更加简洁、可维护，并且功能完整。
