using System.Linq.Expressions;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Common;
using XJ.Framework.Library.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Library.EntityFrameworkCore.QueryBuilders;
using XJ.Framework.Library.EntityFrameworkCore.QueryCriteria;

namespace XJ.Framework.DynamicForm.EntityFrameworkCore.Repositories;

/// <summary>
/// FormInstance 仓储实现
/// </summary>
public class FormInstanceRepository : BaseSoftDeleteRepository<DynamicFormDbContext, long, FormInstanceEntity>,
    IFormInstanceRepository
{
    public FormInstanceRepository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public async Task<PageData<long, FormInstanceEntity>> GetNewestPageAsync(
        Expression<Func<FormInstanceEntity, bool>> whereLambda,
        Dictionary<string, string> dynamicQueries,
        List<FormDataDynamicQuery> formDataDynamicQueries,
        Dictionary<string, SortDirection> dynamicOrderBys,
        Dictionary<string, SortDirection> formDataDynamicOrderBys,
        int rowIndex,
        int pageSize,
        List<OrderbyDirection<FormInstanceEntity>> orderBy,
        bool isNoTracking = true)
    {
        whereLambda = whereLambda.And(q => !q.IsObsoleted);
        whereLambda = whereLambda.And(BuildDynamicQueries(dynamicQueries));
        whereLambda = whereLambda.And(BuildFormDataDynamicQueries(formDataDynamicQueries));


        var queryable = await GetQueryableAsync();
        var data = queryable.Where(whereLambda)
            .Where(q => !queryable.Where(f =>
                    f.Key != q.Key && !f.IsObsoleted && q.BusinessId.ToLower().Equals(f.BusinessId.ToLower()))
                .Any(f => f.VersionTime > q.VersionTime))
            .Orderby<long, FormInstanceEntity>(orderBy);

        var pageData = new PageData<long, FormInstanceEntity>
        {
            Totals = await data.CountAsync(),
            Rows = await data.Skip(rowIndex).Take(pageSize).ToListAsync()
        };
        return pageData;
    }


    public async Task<PageData<long, FormInstanceEntity>> GetPageAsync(
        Expression<Func<FormInstanceEntity, bool>> whereLambda, Dictionary<string, string> dynamicQueries,
        List<FormDataDynamicQuery> formDataDynamicQueries, int rowIndex,
        int pageSize, List<OrderbyDirection<FormInstanceEntity>> orderBy,
        bool isNoTracking = true)
    {
        whereLambda = whereLambda.And(BuildDynamicQueries(dynamicQueries));
        whereLambda = whereLambda.And(BuildFormDataDynamicQueries(formDataDynamicQueries));
        return await GetPageAsync(whereLambda, rowIndex, pageSize, orderBy, isNoTracking);
    }

    private Expression<Func<FormInstanceEntity, bool>> BuildFormDataDynamicQueries(
        List<FormDataDynamicQuery> formDataDynamicQueries)
    {
        var expr = DynamicLinqExpressions.True<FormInstanceEntity>();
        formDataDynamicQueries.ForEach(kv =>
        {
            var key = kv.Key;
            var op = kv.Operator;
            var value = kv.Value;

            expr = expr.And(formInstance =>
                DbContext.Set<FormInstanceDataEntity>().Any(data =>
                    data.BusinessId.ToLower().Equals(formInstance.BusinessId.ToLower()) &&
                    data.Version.ToLower().Equals(formInstance.Version.ToLower()) &&
                    data.FormCode.ToLower().Equals(formInstance.FormCode.ToLower()) &&
                    data.FormVersion.ToLower().Equals(formInstance.FormVersion.ToLower()) &&
                    data.Code == key &&
                    !data.Deleted &&
                    (op == FormDataQueryOperator.Equal ? data.Value != null && data.Value.Equals(value) :
                        op == FormDataQueryOperator.NotEqual ? data.Value != null && !data.Value.Equals(value) :
                        op == FormDataQueryOperator.Empty ? data.Value == null || string.IsNullOrEmpty(data.Value) :
                        op == FormDataQueryOperator.NotEmpty ? data.Value != null && !string.IsNullOrEmpty(data.Value) :
                        false)
                )
            );
        });
        return expr;
    }


    private Expression<Func<FormInstanceEntity, bool>> BuildDynamicQueries(Dictionary<string, string> dynamicQueries)
    {
        var expr = DynamicLinqExpressions.True<FormInstanceEntity>();
        dynamicQueries.ForEach(kv =>
        {
            var key = kv.Key;
            var value = kv.Value;
            if (value.IsNullOrEmpty())
                return;

            expr = expr.And(formInstance =>
                DbContext.Set<FormFieldInstanceEntity>().Any(fieldInstance =>
                    fieldInstance.BusinessId.ToLower().Equals(formInstance.BusinessId.ToLower()) &&
                    fieldInstance.Version.ToLower().Equals(formInstance.Version.ToLower()) &&
                    fieldInstance.FormCode.ToLower().Equals(formInstance.FormCode.ToLower()) &&
                    fieldInstance.FormVersion.ToLower().Equals(formInstance.FormVersion.ToLower()) &&
                    fieldInstance.Code == key &&
                    fieldInstance.JsonValue != null && fieldInstance.JsonValue.Contains(value) &&
                    !fieldInstance.Deleted
                )
            );
        });

        return expr;
    }

    public async Task<int> GetCountAsync(Expression<Func<FormInstanceEntity, bool>> whereLambda,
        List<FormDataDynamicQuery> formDataDynamicQueries)
    {
        whereLambda = whereLambda.And(q => !q.IsObsoleted);
        whereLambda = whereLambda.And(BuildFormDataDynamicQueries(formDataDynamicQueries));


        var queryable = await GetQueryableAsync();
        var data = queryable.Where(whereLambda)
            .Where(q => !queryable.Where(f =>
                    f.Key != q.Key && !f.IsObsoleted && q.BusinessId.ToLower().Equals(f.BusinessId.ToLower()))
                .Any(f => f.VersionTime > q.VersionTime));
        return await data.CountAsync();
    }


    public async Task<PageData<long, FormInstanceEntity>> GetNewestPageRawSqlAsync(
        List<WhereItem> whereItems,
        Dictionary<string, string> dynamicQueries,
        List<FormDataDynamicQuery> formDataDynamicQueries,
        List<OrderByItem> orderByItems,
        Dictionary<string, SortDirection> dynamicOrderBys,
        Dictionary<string, SortDirection> formDataDynamicOrderBys,
        int rowIndex,
        int pageSize)
    {
        /*
         * whereLambda = whereLambda.And(q => !q.IsObsoleted);
           whereLambda = whereLambda.And(BuildDynamicQueries(dynamicQueries));
           whereLambda = whereLambda.And(BuildFormDataDynamicQueries(formDataDynamicQueries));


           var queryable = await GetQueryableAsync();
           var data = queryable.Where(whereLambda)
               .Where(q => !queryable.Where(f =>
                       f.Key != q.Key && !f.IsObsoleted && q.BusinessId.ToLower().Equals(f.BusinessId.ToLower()))
                   .Any(f => f.VersionTime > q.VersionTime))
               .Orderby<long, FormInstanceEntity>(orderBy);
         */

        whereItems.Add(WhereItemFactory.Equal("is_obsoleted", false));
        whereItems.Add(WhereItemFactory.IsNotNull("business_id"));
        whereItems.Add(WhereItemFactory.NotExists(@"
            SELECT 1 FROM form_instances f_inner 
            WHERE f_inner.id != form_instances.id 
                AND f_inner.is_obsoleted = 0 
                AND LOWER(f_inner.business_id) = LOWER(form_instances.business_id)
                AND f_inner.version_time > form_instances.version_time"));

        var parameters = new Dictionary<string, object>();

        var whereClause = WhereItemBuilder.BuildWhereClause(whereItems, parameters);
        
        var orderByClause = OrderByItemBuilder.BuildOrderByClause(orderByItems);


        throw new NotImplementedException();
    }
}
