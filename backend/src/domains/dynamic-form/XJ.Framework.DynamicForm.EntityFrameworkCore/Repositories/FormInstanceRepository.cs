using System.Linq.Expressions;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Common;
using XJ.Framework.Library.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Library.EntityFrameworkCore.QueryBuilders;
using XJ.Framework.Library.EntityFrameworkCore.QueryCriteria;

namespace XJ.Framework.DynamicForm.EntityFrameworkCore.Repositories;

/// <summary>
/// FormInstance 仓储实现
/// </summary>
public class FormInstanceRepository : BaseSoftDeleteRepository<DynamicFormDbContext, long, FormInstanceEntity>,
    IFormInstanceRepository
{
    public FormInstanceRepository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public async Task<PageData<long, FormInstanceEntity>> GetNewestPageAsync(
        Expression<Func<FormInstanceEntity, bool>> whereLambda,
        Dictionary<string, string> dynamicQueries,
        List<FormDataDynamicQuery> formDataDynamicQueries,
        Dictionary<string, SortDirection> dynamicOrderBys,
        Dictionary<string, SortDirection> formDataDynamicOrderBys,
        int rowIndex,
        int pageSize,
        List<OrderbyDirection<FormInstanceEntity>> orderBy,
        bool isNoTracking = true)
    {
        whereLambda = whereLambda.And(q => !q.IsObsoleted);
        whereLambda = whereLambda.And(BuildDynamicQueries(dynamicQueries));
        whereLambda = whereLambda.And(BuildFormDataDynamicQueries(formDataDynamicQueries));


        var queryable = await GetQueryableAsync();
        var data = queryable.Where(whereLambda)
            .Where(q => !queryable.Where(f =>
                    f.Key != q.Key && !f.IsObsoleted && q.BusinessId.ToLower().Equals(f.BusinessId.ToLower()))
                .Any(f => f.VersionTime > q.VersionTime))
            .Orderby<long, FormInstanceEntity>(orderBy);

        var pageData = new PageData<long, FormInstanceEntity>
        {
            Totals = await data.CountAsync(),
            Rows = await data.Skip(rowIndex).Take(pageSize).ToListAsync()
        };
        return pageData;
    }


    public async Task<PageData<long, FormInstanceEntity>> GetPageAsync(
        Expression<Func<FormInstanceEntity, bool>> whereLambda, Dictionary<string, string> dynamicQueries,
        List<FormDataDynamicQuery> formDataDynamicQueries, int rowIndex,
        int pageSize, List<OrderbyDirection<FormInstanceEntity>> orderBy,
        bool isNoTracking = true)
    {
        whereLambda = whereLambda.And(BuildDynamicQueries(dynamicQueries));
        whereLambda = whereLambda.And(BuildFormDataDynamicQueries(formDataDynamicQueries));
        return await GetPageAsync(whereLambda, rowIndex, pageSize, orderBy, isNoTracking);
    }

    private Expression<Func<FormInstanceEntity, bool>> BuildFormDataDynamicQueries(
        List<FormDataDynamicQuery> formDataDynamicQueries)
    {
        var expr = DynamicLinqExpressions.True<FormInstanceEntity>();
        formDataDynamicQueries.ForEach(kv =>
        {
            var key = kv.Key;
            var op = kv.Operator;
            var value = kv.Value;

            expr = expr.And(formInstance =>
                DbContext.Set<FormInstanceDataEntity>().Any(data =>
                    data.BusinessId.ToLower().Equals(formInstance.BusinessId.ToLower()) &&
                    data.Version.ToLower().Equals(formInstance.Version.ToLower()) &&
                    data.FormCode.ToLower().Equals(formInstance.FormCode.ToLower()) &&
                    data.FormVersion.ToLower().Equals(formInstance.FormVersion.ToLower()) &&
                    data.Code == key &&
                    !data.Deleted &&
                    (op == FormDataQueryOperator.Equal ? data.Value != null && data.Value.Equals(value) :
                        op == FormDataQueryOperator.NotEqual ? data.Value != null && !data.Value.Equals(value) :
                        op == FormDataQueryOperator.Empty ? data.Value == null || string.IsNullOrEmpty(data.Value) :
                        op == FormDataQueryOperator.NotEmpty ? data.Value != null && !string.IsNullOrEmpty(data.Value) :
                        false)
                )
            );
        });
        return expr;
    }


    private Expression<Func<FormInstanceEntity, bool>> BuildDynamicQueries(Dictionary<string, string> dynamicQueries)
    {
        var expr = DynamicLinqExpressions.True<FormInstanceEntity>();
        dynamicQueries.ForEach(kv =>
        {
            var key = kv.Key;
            var value = kv.Value;
            if (value.IsNullOrEmpty())
                return;

            expr = expr.And(formInstance =>
                DbContext.Set<FormFieldInstanceEntity>().Any(fieldInstance =>
                    fieldInstance.BusinessId.ToLower().Equals(formInstance.BusinessId.ToLower()) &&
                    fieldInstance.Version.ToLower().Equals(formInstance.Version.ToLower()) &&
                    fieldInstance.FormCode.ToLower().Equals(formInstance.FormCode.ToLower()) &&
                    fieldInstance.FormVersion.ToLower().Equals(formInstance.FormVersion.ToLower()) &&
                    fieldInstance.Code == key &&
                    fieldInstance.JsonValue != null && fieldInstance.JsonValue.Contains(value) &&
                    !fieldInstance.Deleted
                )
            );
        });

        return expr;
    }

    public async Task<int> GetCountAsync(Expression<Func<FormInstanceEntity, bool>> whereLambda,
        List<FormDataDynamicQuery> formDataDynamicQueries)
    {
        whereLambda = whereLambda.And(q => !q.IsObsoleted);
        whereLambda = whereLambda.And(BuildFormDataDynamicQueries(formDataDynamicQueries));


        var queryable = await GetQueryableAsync();
        var data = queryable.Where(whereLambda)
            .Where(q => !queryable.Where(f =>
                    f.Key != q.Key && !f.IsObsoleted && q.BusinessId.ToLower().Equals(f.BusinessId.ToLower()))
                .Any(f => f.VersionTime > q.VersionTime));
        return await data.CountAsync();
    }

    /// <summary>
    /// 构建最新版本查询的完整SQL和参数
    /// </summary>
    /// <param name="whereItems">WHERE条件项</param>
    /// <param name="dynamicQueries">动态查询条件</param>
    /// <param name="formDataDynamicQueries">表单数据动态查询条件</param>
    /// <param name="orderByItems">排序条件项</param>
    /// <param name="dynamicOrderBys">动态排序条件</param>
    /// <param name="formDataDynamicOrderBys">表单数据动态排序条件</param>
    /// <returns>完整SQL和参数字典</returns>
    private (string completeSql, string orderByClause, Dictionary<string, object> parameters) BuildNewestQuerySql(
        List<WhereItem> whereItems,
        Dictionary<string, string> dynamicQueries,
        List<FormDataDynamicQuery> formDataDynamicQueries,
        List<OrderByItem> orderByItems,
        Dictionary<string, SortDirection> dynamicOrderBys,
        Dictionary<string, SortDirection> formDataDynamicOrderBys)
    {
        // 添加最新版本的基础条件
        whereItems.Add(WhereItemFactory.Equal("obsoleted", false));
        whereItems.Add(WhereItemFactory.Equal("is_deleted", false));
        whereItems.Add(WhereItemFactory.NotExists(@"
            SELECT 1 FROM d.form_instances f_inner
            WHERE f_inner.id != form_instances.id
                AND f_inner.obsoleted = 0 AND f_inner.is_deleted = 0
                AND LOWER(f_inner.business_id) = LOWER(form_instances.business_id)
                AND f_inner.version_time > form_instances.version_time"));

        // 添加动态查询条件
        AddDynamicWhereItems(whereItems, dynamicQueries);
        AddFormDataDynamicWhereItems(whereItems, formDataDynamicQueries);

        // 添加动态排序条件
        AddDynamicOrderByItems(orderByItems, dynamicOrderBys);
        AddFormDataDynamicOrderByItems(orderByItems, formDataDynamicOrderBys);

        // 构建SQL
        var parameters = new Dictionary<string, object>();
        var whereClause = WhereItemBuilder.BuildWhereClause(whereItems, parameters);
        var orderByClause = OrderByItemBuilder.BuildOrderByClause(orderByItems);
        var completeSql = $"SELECT * FROM d.form_instances {whereClause}";

        return (completeSql, orderByClause, parameters);
    }

    /// <summary>
    /// 获取最新版本记录的总数
    /// </summary>
    /// <param name="whereItems">WHERE条件项</param>
    /// <param name="dynamicQueries">动态查询条件</param>
    /// <param name="formDataDynamicQueries">表单数据动态查询条件</param>
    /// <returns>记录总数</returns>
    public async Task<long> GetNewestCountRawSqlAsync(
        List<WhereItem> whereItems,
        Dictionary<string, string> dynamicQueries,
        List<FormDataDynamicQuery> formDataDynamicQueries)
    {
        // 对于计数查询，不需要排序条件
        var emptyOrderByItems = new List<OrderByItem>();
        var emptyDynamicOrderBys = new Dictionary<string, SortDirection>();
        var emptyFormDataDynamicOrderBys = new Dictionary<string, SortDirection>();

        // 构建完整查询SQL
        var (completeSql, _, parameters) = BuildNewestQuerySql(
            whereItems, dynamicQueries, formDataDynamicQueries,
            emptyOrderByItems, emptyDynamicOrderBys, emptyFormDataDynamicOrderBys);

        // 构建计数SQL
        var countSql = $"SELECT COUNT(*) FROM ({completeSql}) AS subquery";

        // 执行count查询
        var totalCount = await this.DbContext.Database
            .SqlQueryRaw<long>(countSql, parameters.Values.ToArray())
            .FirstOrDefaultAsync();

        return totalCount;
    }

    public async Task<PageData<long, FormInstanceEntity>> GetNewestPageRawSqlAsync(
        List<WhereItem> whereItems,
        Dictionary<string, string> dynamicQueries,
        List<FormDataDynamicQuery> formDataDynamicQueries,
        List<OrderByItem> orderByItems,
        Dictionary<string, SortDirection> dynamicOrderBys,
        Dictionary<string, SortDirection> formDataDynamicOrderBys,
        int rowIndex,
        int pageSize)
    {
        // 构建完整查询SQL
        var (completeSql, orderByClause, parameters) = BuildNewestQuerySql(
            whereItems, dynamicQueries, formDataDynamicQueries,
            orderByItems, dynamicOrderBys, formDataDynamicOrderBys);

        // 构建分页和计数SQL
        var countSql = $"SELECT COUNT(*) FROM ({completeSql}) AS subquery";
        var pageSql = $"{completeSql} {orderByClause} OFFSET {rowIndex} ROWS FETCH NEXT {pageSize} ROWS ONLY";

        // 执行查询
        var data = this.DbSet.FromSqlRaw(pageSql, parameters.Values.ToArray());

        // 执行count查询获取总数
        var totalCount = await this.DbContext.Database
            .SqlQueryRaw<long>(countSql, parameters.Values.ToArray())
            .FirstOrDefaultAsync();

        var pageData = new PageData<long, FormInstanceEntity>
        {
            Totals = totalCount,
            Rows = await data.ToListAsync()
        };

        return pageData;
    }

    private void AddFormDataDynamicOrderByItems(List<OrderByItem> orderByItems,
        Dictionary<string, SortDirection> formDataDynamicOrderBys)
    {
        formDataDynamicOrderBys.ForEach(kv =>
        {
            orderByItems.Add(new OrderByItem()
            {
                DataField = $@"
                    (select value
                        from d.form_instance_datas
                        where business_id = form_instances.business_id
                          and version = form_instances.version
                          and form_code = form_instances.form_code
                          and form_version = form_instances.form_version
                          and is_deleted = 0
                          and code = N'{kv.Key}')",
                SortDirection = kv.Value == SortDirection.Ascending
                    ? OrderByDirection.Ascending
                    : OrderByDirection.Descending
            });
        });
    }

    private void AddDynamicOrderByItems(List<OrderByItem> orderByItems,
        Dictionary<string, SortDirection> dynamicOrderBys)
    {
        dynamicOrderBys.ForEach(kv =>
        {
            orderByItems.Add(new OrderByItem()
            {
                DataField = $@"
                    (select json_value
                        from d.form_field_instances
                        where business_id = form_instances.business_id
                          and version = form_instances.version
                          and form_code = form_instances.form_code
                          and form_version = form_instances.form_version
                          and is_deleted = 0
                          and code = N'{kv.Key}')",
                SortDirection = kv.Value == SortDirection.Ascending
                    ? OrderByDirection.Ascending
                    : OrderByDirection.Descending
            });
        });
    }

    private void AddFormDataDynamicWhereItems(
        List<WhereItem> whereItems, List<FormDataDynamicQuery> formDataDynamicQueries)
    {
        formDataDynamicQueries.ForEach(kv =>
        {
            var key = kv.Key;
            var op = kv.Operator;
            var value = kv.Value;
            if (value.IsNullOrEmpty())
                return;

            // 获取动态查询SQL和参数
            var (dynamicSql, dynamicParams) = GetFormDataDynamicQuerySql(op, value);

            // 调整动态SQL中的参数占位符索引（因为{0}被key占用，所以从{1}开始）
            var adjustedDynamicSql = dynamicSql;
            for (int i = dynamicParams.Length - 1; i >= 0; i--)
            {
                adjustedDynamicSql = adjustedDynamicSql.Replace($"{{{i}}}", $"{{{i + 1}}}");
            }

            // 构建完整的EXISTS查询
            var existsSql = @"
                EXISTS (
                    SELECT 1 FROM d.form_instance_datas fid
                    WHERE fid.business_id = form_instances.business_id
                        AND fid.version = form_instances.version
                        AND fid.form_code = form_instances.form_code
                        AND fid.form_version = form_instances.form_version
                        AND fid.code = {0}
                        AND fid.is_deleted = 0
                        AND " + adjustedDynamicSql + ")";

            // 合并所有参数：key + 动态参数
            var allParams = new List<object> { key };
            allParams.AddRange(dynamicParams);

            whereItems.Add(WhereItemFactory.RawSql(existsSql, allParams.ToArray()));
        });
    }

    /// <summary>
    /// 获取表单数据动态查询SQL条件和参数
    /// </summary>
    /// <param name="op">查询操作符</param>
    /// <param name="value">查询值</param>
    /// <returns>SQL条件片段和参数数组</returns>
    private (string sql, object[] parameters) GetFormDataDynamicQuerySql(FormDataQueryOperator op, string? value)
    {
        // 对于需要值的操作符，如果值为null或空，返回false条件
        if (string.IsNullOrEmpty(value) && (op == FormDataQueryOperator.Equal || op == FormDataQueryOperator.NotEqual ||
                                            op == FormDataQueryOperator.In))
        {
            return ("1=0", new object[0]);
        }

        switch (op)
        {
            case FormDataQueryOperator.Equal:
                return ("fid.value = {0}", new object[] { value! });

            case FormDataQueryOperator.NotEqual:
                return ("fid.value != {0}", new object[] { value! });

            case FormDataQueryOperator.Empty:
                return ("(fid.value IS NULL OR fid.value = '')", new object[0]);

            case FormDataQueryOperator.NotEmpty:
                return ("(fid.value IS NOT NULL AND fid.value != '')", new object[0]);

            case FormDataQueryOperator.In:
                // 处理IN操作符，需要解析逗号分隔的值
                var values = value!.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(v => v.Trim().Trim('\'', '"')) // 移除可能的引号
                    .Where(v => !string.IsNullOrEmpty(v))
                    .ToArray();

                if (!values.Any())
                    return ("1=0", new object[0]); // 空值返回false条件

                // 构建IN条件的占位符
                var placeholders = values.Select((_, index) => $"{{{index}}}").ToArray();
                var inSql = $"fid.value IN ({string.Join(", ", placeholders)})";

                return (inSql, values.Cast<object>().ToArray());

            default:
                return ("1=0", new object[0]); // 未知操作符返回false条件
        }
    }

    private void AddDynamicWhereItems(List<WhereItem> whereItems, Dictionary<string, string> dynamicQueries)
    {
        dynamicQueries.ForEach(kv =>
        {
            var key = kv.Key;
            var value = kv.Value;
            if (value.IsNullOrEmpty())
                return;

            whereItems.Add(WhereItemFactory.RawSql(@"
                EXISTS (
                    SELECT 1 FROM d.form_field_instances ffi
                    WHERE ffi.business_id = form_instances.business_id
                        AND ffi.version = form_instances.version
                        AND ffi.form_code = form_instances.form_code
                        AND ffi.form_version = form_instances.form_version
                        AND ffi.code = {0}
                        AND ffi.json_value IS NOT NULL
                        AND ffi.json_value LIKE {1}
                        AND ffi.is_deleted = 0
                )", key, $"%{value}%"));
        });
    }
}
