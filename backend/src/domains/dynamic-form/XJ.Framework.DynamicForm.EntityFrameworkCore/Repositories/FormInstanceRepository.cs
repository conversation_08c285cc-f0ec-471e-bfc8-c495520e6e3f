using System.Linq.Expressions;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Common;
using XJ.Framework.Library.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Library.EntityFrameworkCore.QueryBuilders;
using XJ.Framework.Library.EntityFrameworkCore.QueryCriteria;

namespace XJ.Framework.DynamicForm.EntityFrameworkCore.Repositories;

/// <summary>
/// FormInstance 仓储实现
/// </summary>
public class FormInstanceRepository : BaseSoftDeleteRepository<DynamicFormDbContext, long, FormInstanceEntity>,
    IFormInstanceRepository
{
    public FormInstanceRepository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public async Task<PageData<long, FormInstanceEntity>> GetNewestPageAsync(
        Expression<Func<FormInstanceEntity, bool>> whereLambda,
        Dictionary<string, string> dynamicQueries,
        List<FormDataDynamicQuery> formDataDynamicQueries,
        Dictionary<string, SortDirection> dynamicOrderBys,
        Dictionary<string, SortDirection> formDataDynamicOrderBys,
        int rowIndex,
        int pageSize,
        List<OrderbyDirection<FormInstanceEntity>> orderBy,
        bool isNoTracking = true)
    {
        whereLambda = whereLambda.And(q => !q.IsObsoleted);
        whereLambda = whereLambda.And(BuildDynamicQueries(dynamicQueries));
        whereLambda = whereLambda.And(BuildFormDataDynamicQueries(formDataDynamicQueries));


        var queryable = await GetQueryableAsync();
        var data = queryable.Where(whereLambda)
            .Where(q => !queryable.Where(f =>
                    f.Key != q.Key && !f.IsObsoleted && q.BusinessId.ToLower().Equals(f.BusinessId.ToLower()))
                .Any(f => f.VersionTime > q.VersionTime))
            .Orderby<long, FormInstanceEntity>(orderBy);

        var pageData = new PageData<long, FormInstanceEntity>
        {
            Totals = await data.CountAsync(),
            Rows = await data.Skip(rowIndex).Take(pageSize).ToListAsync()
        };
        return pageData;
    }


    public async Task<PageData<long, FormInstanceEntity>> GetPageAsync(
        Expression<Func<FormInstanceEntity, bool>> whereLambda, Dictionary<string, string> dynamicQueries,
        List<FormDataDynamicQuery> formDataDynamicQueries, int rowIndex,
        int pageSize, List<OrderbyDirection<FormInstanceEntity>> orderBy,
        bool isNoTracking = true)
    {
        whereLambda = whereLambda.And(BuildDynamicQueries(dynamicQueries));
        whereLambda = whereLambda.And(BuildFormDataDynamicQueries(formDataDynamicQueries));
        return await GetPageAsync(whereLambda, rowIndex, pageSize, orderBy, isNoTracking);
    }

    private Expression<Func<FormInstanceEntity, bool>> BuildFormDataDynamicQueries(
        List<FormDataDynamicQuery> formDataDynamicQueries)
    {
        var expr = DynamicLinqExpressions.True<FormInstanceEntity>();
        formDataDynamicQueries.ForEach(kv =>
        {
            var key = kv.Key;
            var op = kv.Operator;
            var value = kv.Value;

            expr = expr.And(formInstance =>
                DbContext.Set<FormInstanceDataEntity>().Any(data =>
                    data.BusinessId.ToLower().Equals(formInstance.BusinessId.ToLower()) &&
                    data.Version.ToLower().Equals(formInstance.Version.ToLower()) &&
                    data.FormCode.ToLower().Equals(formInstance.FormCode.ToLower()) &&
                    data.FormVersion.ToLower().Equals(formInstance.FormVersion.ToLower()) &&
                    data.Code == key &&
                    !data.Deleted &&
                    (op == FormDataQueryOperator.Equal ? data.Value != null && data.Value.Equals(value) :
                        op == FormDataQueryOperator.NotEqual ? data.Value != null && !data.Value.Equals(value) :
                        op == FormDataQueryOperator.Empty ? data.Value == null || string.IsNullOrEmpty(data.Value) :
                        op == FormDataQueryOperator.NotEmpty ? data.Value != null && !string.IsNullOrEmpty(data.Value) :
                        false)
                )
            );
        });
        return expr;
    }


    private Expression<Func<FormInstanceEntity, bool>> BuildDynamicQueries(Dictionary<string, string> dynamicQueries)
    {
        var expr = DynamicLinqExpressions.True<FormInstanceEntity>();
        dynamicQueries.ForEach(kv =>
        {
            var key = kv.Key;
            var value = kv.Value;
            if (value.IsNullOrEmpty())
                return;

            expr = expr.And(formInstance =>
                DbContext.Set<FormFieldInstanceEntity>().Any(fieldInstance =>
                    fieldInstance.BusinessId.ToLower().Equals(formInstance.BusinessId.ToLower()) &&
                    fieldInstance.Version.ToLower().Equals(formInstance.Version.ToLower()) &&
                    fieldInstance.FormCode.ToLower().Equals(formInstance.FormCode.ToLower()) &&
                    fieldInstance.FormVersion.ToLower().Equals(formInstance.FormVersion.ToLower()) &&
                    fieldInstance.Code == key &&
                    fieldInstance.JsonValue != null && fieldInstance.JsonValue.Contains(value) &&
                    !fieldInstance.Deleted
                )
            );
        });

        return expr;
    }

    public async Task<int> GetCountAsync(Expression<Func<FormInstanceEntity, bool>> whereLambda,
        List<FormDataDynamicQuery> formDataDynamicQueries)
    {
        whereLambda = whereLambda.And(q => !q.IsObsoleted);
        whereLambda = whereLambda.And(BuildFormDataDynamicQueries(formDataDynamicQueries));


        var queryable = await GetQueryableAsync();
        var data = queryable.Where(whereLambda)
            .Where(q => !queryable.Where(f =>
                    f.Key != q.Key && !f.IsObsoleted && q.BusinessId.ToLower().Equals(f.BusinessId.ToLower()))
                .Any(f => f.VersionTime > q.VersionTime));
        return await data.CountAsync();
    }


    public async Task<PageData<long, FormInstanceEntity>> GetNewestPageRawSqlAsync(
        List<WhereItem> whereItems,
        Dictionary<string, string> dynamicQueries,
        List<FormDataDynamicQuery> formDataDynamicQueries,
        List<OrderByItem> orderByItems,
        Dictionary<string, SortDirection> dynamicOrderBys,
        Dictionary<string, SortDirection> formDataDynamicOrderBys,
        int rowIndex,
        int pageSize)
    {
        
        whereItems.Add(WhereItemFactory.Equal("is_obsoleted", false));
        whereItems.Add(WhereItemFactory.Equal("is_deleted", false));
        whereItems.Add(WhereItemFactory.NotExists(@"
            SELECT 1 FROM d.form_instances f_inner 
            WHERE f_inner.id != form_instances.id 
                AND f_inner.is_obsoleted = 0  and f_inner.is_deleted = 0
                AND LOWER(f_inner.business_id) = LOWER(form_instances.business_id)
                AND f_inner.version_time > form_instances.version_time"));

        AddDynamicWhereItems(whereItems, dynamicQueries);
        
        AddFormDataDynamicWhereItems(whereItems, formDataDynamicQueries);
        
        AddDynamicOrderByItems(orderByItems, dynamicOrderBys);
        AddFormDataDynamicOrderByItems(orderByItems, formDataDynamicOrderBys);
        
        var parameters = new Dictionary<string, object>();

        var whereClause = WhereItemBuilder.BuildWhereClause(whereItems, parameters);

        var orderByClause = OrderByItemBuilder.BuildOrderByClause(orderByItems);

        var baseQuery = "select * from d.form_instances";
        
        var completeSql = $"{baseQuery} {whereClause} {orderByClause}";
        
        
        throw new NotImplementedException();
    }

    private void AddFormDataDynamicOrderByItems(List<OrderByItem> orderByItems,
        Dictionary<string, SortDirection> formDataDynamicOrderBys)
    {
        
    }
    private void AddDynamicOrderByItems(List<OrderByItem> orderByItems,
        Dictionary<string, SortDirection> dynamicOrderBys)
    {
        dynamicOrderBys.ForEach(kv =>
        {
            //这里拼接成查关联表的orderby逻辑 key就是拼接出来的sql
            /*
             * select *
               from d.form_instances
               
               order by(select value
                        from d.form_instance_datas
                        where business_id = form_instances.business_id
                          and version = form_instances.version
                          and form_code = form_instances.form_code
                          and form_version = form_instances.form_version
                          and code = 'FirstSubmitTime')
                   desc
             */
            
        });
    }

    private void AddFormDataDynamicWhereItems(
       List<WhereItem> whereItems, List<FormDataDynamicQuery> formDataDynamicQueries)
    {
        // var expr = DynamicLinqExpressions.True<FormInstanceEntity>();
        // formDataDynamicQueries.ForEach(kv =>
        // {
        //     var key = kv.Key;
        //     var op = kv.Operator;
        //     var value = kv.Value;
        //
        //     expr = expr.And(formInstance =>
        //         DbContext.Set<FormInstanceDataEntity>().Any(data =>
        //             data.BusinessId.ToLower().Equals(formInstance.BusinessId.ToLower()) &&
        //             data.Version.ToLower().Equals(formInstance.Version.ToLower()) &&
        //             data.FormCode.ToLower().Equals(formInstance.FormCode.ToLower()) &&
        //             data.FormVersion.ToLower().Equals(formInstance.FormVersion.ToLower()) &&
        //             data.Code == key &&
        //             !data.Deleted &&
        //             (op == FormDataQueryOperator.Equal ? data.Value != null && data.Value.Equals(value) :
        //                 op == FormDataQueryOperator.NotEqual ? data.Value != null && !data.Value.Equals(value) :
        //                 op == FormDataQueryOperator.Empty ? data.Value == null || string.IsNullOrEmpty(data.Value) :
        //                 op == FormDataQueryOperator.NotEmpty ? data.Value != null && !string.IsNullOrEmpty(data.Value) :
        //                 false)
        //         )
        //     );
        // });
        // return expr;
        formDataDynamicQueries.ForEach(kv =>
        {
            var key = kv.Key;
            var op = kv.Operator;
            var value = kv.Value;
            if (value.IsNullOrEmpty())
                return;

            // 获取动态查询SQL和参数
            var (dynamicSql, dynamicParams) = GetFormDataDynamicQuerySql(op, value);

            // 调整动态SQL中的参数占位符索引（因为{0}被key占用，所以从{1}开始）
            var adjustedDynamicSql = dynamicSql;
            for (int i = dynamicParams.Length - 1; i >= 0; i--)
            {
                adjustedDynamicSql = adjustedDynamicSql.Replace($"{{{i}}}", $"{{{i + 1}}}");
            }

            // 构建完整的EXISTS查询
            var existsSql = @"
                EXISTS (
                    SELECT 1 FROM d.form_instance_data fid
                    WHERE fid.business_id = form_instances.business_id
                        AND fid.version = form_instances.version
                        AND fid.form_code = form_instances.form_code
                        AND fid.form_version = form_instances.form_version
                        AND fid.code = {0}
                        AND fid.deleted = 0
                        AND " + adjustedDynamicSql + ")";

            // 合并所有参数：key + 动态参数
            var allParams = new List<object> { key };
            allParams.AddRange(dynamicParams);

            whereItems.Add(WhereItemFactory.RawSql(existsSql, allParams.ToArray()));
        });
        
    }
    /// <summary>
    /// 获取表单数据动态查询SQL条件和参数
    /// </summary>
    /// <param name="op">查询操作符</param>
    /// <param name="value">查询值</param>
    /// <returns>SQL条件片段和参数数组</returns>
    private (string sql, object[] parameters) GetFormDataDynamicQuerySql(FormDataQueryOperator op, string? value)
    {
        // 对于需要值的操作符，如果值为null或空，返回false条件
        if (string.IsNullOrEmpty(value) && (op == FormDataQueryOperator.Equal || op == FormDataQueryOperator.NotEqual || op == FormDataQueryOperator.In))
        {
            return ("1=0", new object[0]);
        }

        switch (op)
        {
            case FormDataQueryOperator.Equal:
                return ("fid.value = {0}", new object[] { value! });

            case FormDataQueryOperator.NotEqual:
                return ("fid.value != {0}", new object[] { value! });

            case FormDataQueryOperator.Empty:
                return ("(fid.value IS NULL OR fid.value = '')", new object[0]);

            case FormDataQueryOperator.NotEmpty:
                return ("(fid.value IS NOT NULL AND fid.value != '')", new object[0]);

            case FormDataQueryOperator.In:
                // 处理IN操作符，需要解析逗号分隔的值
                var values = value!.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                 .Select(v => v.Trim().Trim('\'', '"')) // 移除可能的引号
                                 .Where(v => !string.IsNullOrEmpty(v))
                                 .ToArray();

                if (!values.Any())
                    return ("1=0", new object[0]); // 空值返回false条件

                // 构建IN条件的占位符
                var placeholders = values.Select((_, index) => $"{{{index}}}").ToArray();
                var inSql = $"fid.value IN ({string.Join(", ", placeholders)})";

                return (inSql, values.Cast<object>().ToArray());

            default:
                return ("1=0", new object[0]); // 未知操作符返回false条件
        }
    }

    private void AddDynamicWhereItems(List<WhereItem> whereItems, Dictionary<string, string> dynamicQueries)
    {
        dynamicQueries.ForEach(kv =>
        {
            var key = kv.Key;
            var value = kv.Value;
            if (value.IsNullOrEmpty())
                return;

            whereItems.Add(WhereItemFactory.RawSql(@"
                EXISTS (
                    SELECT 1 FROM d.form_field_instances ffi
                    WHERE ffi.business_id = form_instances.business_id
                        AND ffi.version = form_instances.version
                        AND ffi.form_code = form_instances.form_code
                        AND ffi.form_version = form_instances.form_version
                        AND ffi.code = {0}
                        AND ffi.json_value IS NOT NULL
                        AND ffi.json_value LIKE {1}
                        AND ffi.deleted = 0
                )", key, $"%{value}%"));
        });
    }
}
