# FromSqlRaw类型转换问题修复总结

## 问题描述

在FormInstanceRepository中遇到了两个关键问题：

1. **IQueryable没有FromSqlRaw方法**：
   - `IQueryable<T>`接口本身没有`FromSqlRaw`方法
   - `FromSqlRaw`是Entity Framework Core的`DbSet<T>`的扩展方法

2. **类型转换错误**：
   - Count查询返回的是实体对象，但需要的是数值结果
   - 错误：`无法将类型"FormInstanceEntity"隐式转换为"long"`

## 原始问题代码

```csharp
// ❌ 问题代码
var queryable = await GetQueryableAsync();
var data = queryable.FromSqlRaw(completeSql, parameters.ToArray()); // IQueryable没有FromSqlRaw方法

var pageData = new PageData<long, FormInstanceEntity>
{
    // ❌ 类型转换错误：FromSqlRaw返回实体，但需要long类型
    Totals = await queryable.FromSqlRaw(countSql, parameters.ToArray()).FirstOrDefaultAsync(),
    Rows = await data.ToListAsync()
};
```

## 修复后的正确代码

```csharp
// ✅ 修复后的代码
var data = this.DbSet.FromSqlRaw(pageSql, parameters.ToArray());

// 执行count查询获取总数
var totalCount = await this.DbContext.Database
    .SqlQueryRaw<long>(countSql, parameters.ToArray())
    .FirstOrDefaultAsync();
    
var pageData = new PageData<long, FormInstanceEntity>
{
    Totals = totalCount,
    Rows = await data.ToListAsync()
};
```

## 关键修复点

### 1. 使用正确的DbSet.FromSqlRaw
- **问题**：`IQueryable<T>`没有`FromSqlRaw`方法
- **解决**：使用`this.DbSet.FromSqlRaw()`，DbSet继承自IQueryable并提供了FromSqlRaw扩展

### 2. 使用Database.SqlQueryRaw<T>执行标量查询
- **问题**：Count查询需要返回数值，不是实体对象
- **解决**：使用`DbContext.Database.SqlQueryRaw<long>()`执行原生SQL并返回标量值

### 3. 正确的SQL语句使用
- **数据查询**：使用`pageSql`（包含分页的SQL）
- **计数查询**：使用`countSql`（COUNT查询的SQL）

## Entity Framework Core中的SQL执行方法对比

### FromSqlRaw - 用于实体查询
```csharp
// ✅ 正确用法：查询实体对象
var entities = dbSet.FromSqlRaw("SELECT * FROM Users WHERE Status = {0}", 1);
var userList = await entities.ToListAsync(); // 返回List<User>

// ❌ 错误用法：用于标量查询
var count = await dbSet.FromSqlRaw("SELECT COUNT(*) FROM Users").FirstOrDefaultAsync(); // 类型错误
```

### SqlQueryRaw - 用于标量查询
```csharp
// ✅ 正确用法：查询标量值
var count = await dbContext.Database.SqlQueryRaw<long>("SELECT COUNT(*) FROM Users").FirstOrDefaultAsync();
var maxId = await dbContext.Database.SqlQueryRaw<int>("SELECT MAX(Id) FROM Users").FirstOrDefaultAsync();

// ✅ 也可以查询简单的值类型集合
var ids = await dbContext.Database.SqlQueryRaw<long>("SELECT Id FROM Users WHERE Status = {0}", 1).ToListAsync();
```

### ExecuteSqlRaw - 用于非查询操作
```csharp
// ✅ 正确用法：执行INSERT、UPDATE、DELETE等操作
var affectedRows = await dbContext.Database.ExecuteSqlRawAsync("UPDATE Users SET Status = {0} WHERE Id = {1}", 1, 123);
```

## 最佳实践

### 1. 选择正确的方法
- **查询实体对象** → 使用`DbSet.FromSqlRaw<TEntity>()`
- **查询标量值** → 使用`Database.SqlQueryRaw<T>()`
- **执行非查询操作** → 使用`Database.ExecuteSqlRaw()`

### 2. 参数化查询
```csharp
// ✅ 正确：使用参数化查询
var result = await dbContext.Database.SqlQueryRaw<long>(
    "SELECT COUNT(*) FROM Users WHERE Status = {0} AND CreatedDate > {1}", 
    status, date).FirstOrDefaultAsync();

// ❌ 错误：字符串拼接（SQL注入风险）
var result = await dbContext.Database.SqlQueryRaw<long>(
    $"SELECT COUNT(*) FROM Users WHERE Status = {status}").FirstOrDefaultAsync();
```

### 3. 类型匹配
```csharp
// ✅ 正确：确保SQL返回的类型与泛型参数匹配
var count = await dbContext.Database.SqlQueryRaw<long>("SELECT COUNT(*) FROM Users").FirstOrDefaultAsync();
var average = await dbContext.Database.SqlQueryRaw<decimal>("SELECT AVG(Score) FROM Users").FirstOrDefaultAsync();

// ❌ 错误：类型不匹配
var count = await dbContext.Database.SqlQueryRaw<int>("SELECT COUNT(*) FROM Users").FirstOrDefaultAsync(); // COUNT(*)返回long
```

## 性能考虑

### 1. 分页查询优化
```csharp
// ✅ 推荐：使用OFFSET...FETCH NEXT进行分页
var pageSql = $"{baseSql} ORDER BY Id OFFSET {offset} ROWS FETCH NEXT {pageSize} ROWS ONLY";

// ✅ 计数查询优化：使用子查询避免重复的WHERE条件
var countSql = $"SELECT COUNT(*) FROM ({baseSql}) AS subquery";
```

### 2. 参数重用
```csharp
// ✅ 好的做法：为数据查询和计数查询重用相同的参数
var dataResult = this.DbSet.FromSqlRaw(pageSql, parameters.ToArray());
var countResult = await this.DbContext.Database.SqlQueryRaw<long>(countSql, parameters.ToArray()).FirstOrDefaultAsync();
```

## 构建状态

- ✅ 编译成功（0错误，0警告）
- ✅ 修复了类型转换问题
- ✅ 修复了方法调用问题
- ✅ 保持了原有的功能逻辑

## 总结

这次修复解决了Entity Framework Core中常见的两个问题：
1. 正确使用`DbSet.FromSqlRaw`而不是`IQueryable.FromSqlRaw`
2. 使用`Database.SqlQueryRaw<T>`执行标量查询而不是实体查询

修复后的代码更加安全、类型正确，并且遵循了Entity Framework Core的最佳实践。
