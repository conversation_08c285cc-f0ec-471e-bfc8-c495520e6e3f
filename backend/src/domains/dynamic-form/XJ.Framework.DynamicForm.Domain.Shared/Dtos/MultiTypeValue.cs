using System.Text.Json.Serialization;
using XJ.Framework.DynamicForm.Application.JsonConverters;

namespace XJ.Framework.DynamicForm.Domain.Shared.Dtos;

[JsonConverter(typeof(MultiTypeValueConverter))]
public class MultiTypeValue
{
    public string? StringValue { get; set; }

    public int? IntValue { get; set; }
    public List<string>? StringArray { get; set; }
    public Dictionary<string, object?>? KeyValue { get; set; }
    public List<Dictionary<string, object?>>? ObjectArray { get; set; }
}