<Project Sdk="Microsoft.NET.Sdk">


    <Import Project="..\..\..\Common.Secrets.props"/>
    <Import Project="..\..\..\Common.props"/>

    <ItemGroup>
        <ProjectReference Include="..\..\core\abstractions\XJ.Framework.Library.Common.Abstraction\XJ.Framework.Library.Common.Abstraction.csproj"/>
        <ProjectReference Include="..\XJ.Framework.Library.Domain\XJ.Framework.Library.Domain.csproj"/>
        <ProjectReference Include="..\XJ.Framework.Library.Domain.Shared\XJ.Framework.Library.Domain.Shared.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <Compile Remove="Interfaces\IBaseAuditService.cs"/>
        <Compile Remove="Interfaces\IBaseSoftDeleteService.cs"/>
    </ItemGroup>

</Project>
