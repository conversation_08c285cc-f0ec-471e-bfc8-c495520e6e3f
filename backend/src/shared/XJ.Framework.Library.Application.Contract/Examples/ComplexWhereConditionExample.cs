using XJ.Framework.Library.Application.Contract.Extensions;
using XJ.Framework.Library.EntityFrameworkCore.QueryCriteria;

namespace XJ.Framework.Library.Application.Contract.Examples;

/// <summary>
/// 复杂WHERE条件示例
/// </summary>
public class ComplexWhereConditionExample
{
    /// <summary>
    /// 示例1：处理您提到的复杂WHERE条件
    /// 原始条件：var data = queryable.Where(whereLambda)
    ///     .Where(q => !queryable.Where(f =>
    ///         f.Key != q.Key &amp;&amp; !f.IsObsoleted &amp;&amp; q.BusinessId.ToLower().Equals(f.BusinessId.ToLower()))
    ///     .Any(f => f.VersionTime > q.VersionTime));
    /// </summary>
    public void FormInstanceRepositoryExample()
    {
        // 基础查询条件（假设这是从PagedQueryCriteria转换来的）
        var baseWhereItems = new List<WhereItem>
        {
            WhereItemFactory.Equal("is_obsoleted", false),
            WhereItemFactory.IsNotNull("business_id")
        };

        // 复杂的NOT EXISTS条件：查找最新版本的记录
        var notExistsSubQuery = @"
            SELECT 1 FROM form_instances f2 
            WHERE f2.key != f1.key 
                AND f2.is_obsoleted = 0 
                AND LOWER(f2.business_id) = LOWER(f1.business_id)
                AND f2.version_time > f1.version_time";

        // 添加复杂条件
        baseWhereItems.Add(WhereItemFactory.NotExists(notExistsSubQuery));

        // 构建最终SQL
        var whereClause = SqlQueryBuilderExtensions.BuildWhereClause(baseWhereItems, out var parameters);

        Console.WriteLine("复杂WHERE条件SQL:");
        Console.WriteLine(whereClause);
        Console.WriteLine("参数:");
        foreach (var param in parameters)
        {
            Console.WriteLine($"{param.Key}: {param.Value}");
        }

        // 完整的查询示例
        var baseQuery = "SELECT f1.* FROM form_instances f1";
        var completeSql = $"{baseQuery} {whereClause}";
        
        Console.WriteLine("\n完整SQL:");
        Console.WriteLine(completeSql);
    }

    /// <summary>
    /// 示例2：使用原生SQL片段处理复杂条件
    /// </summary>
    public void RawSqlExample()
    {
        var whereItems = new List<WhereItem>
        {
            WhereItemFactory.Equal("status", 1),

            // 单个参数的原生SQL
            WhereItemFactory.RawSql("LOWER(business_id) = LOWER({0})", "FORM_001"),

            // 多个参数的原生SQL
            WhereItemFactory.RawSql("DATE(created_time) BETWEEN {0} AND {1}",
                DateTime.Now.AddDays(-30), DateTime.Now),

            // JSON字段查询（如果使用MySQL或PostgreSQL）
            WhereItemFactory.RawSql("JSON_EXTRACT(metadata, '$.type') = {0}", "dynamic_form"),

            // 复杂的多参数条件
            WhereItemFactory.RawSql("(priority BETWEEN {0} AND {1}) OR (urgent = {2})",
                1, 5, true),

            // 无参数的原生SQL
            WhereItemFactory.RawSqlNoParams("deleted_at IS NULL")
        };

        var whereClause = SqlQueryBuilderExtensions.BuildWhereClause(whereItems, out var parameters);

        Console.WriteLine("原生SQL片段示例:");
        Console.WriteLine(whereClause);
        Console.WriteLine("参数:");
        foreach (var param in parameters)
        {
            Console.WriteLine($"{param.Key}: {param.Value}");
        }
    }

    /// <summary>
    /// 示例2.1：多参数RawSql的详细使用示例
    /// </summary>
    public void MultiParameterRawSqlExample()
    {
        var whereItems = new List<WhereItem>
        {
            // 基础条件
            WhereItemFactory.Equal("is_active", true),

            // 多参数的复杂日期范围查询
            WhereItemFactory.RawSql(
                "created_time BETWEEN {0} AND {1} AND updated_time > {2}",
                DateTime.Now.AddDays(-30),
                DateTime.Now,
                DateTime.Now.AddDays(-7)
            ),

            // 多参数的字符串匹配
            WhereItemFactory.RawSql(
                "(name LIKE {0} OR description LIKE {1}) AND category = {2}",
                "%admin%",
                "%管理%",
                "system"
            ),

            // 多参数的数值范围和状态检查
            WhereItemFactory.RawSql(
                "score BETWEEN {0} AND {1} AND status IN ({2}, {3}, {4})",
                80,
                100,
                "active",
                "pending",
                "approved"
            ),

            // 复杂的JSON查询（多个JSON字段）
            WhereItemFactory.RawSql(
                "JSON_EXTRACT(settings, '$.enabled') = {0} AND JSON_EXTRACT(metadata, '$.version') >= {1}",
                true,
                "2.0"
            )
        };

        var whereClause = SqlQueryBuilderExtensions.BuildWhereClause(whereItems, out var parameters);

        Console.WriteLine("多参数RawSql示例:");
        Console.WriteLine(whereClause);
        Console.WriteLine("参数:");
        foreach (var param in parameters)
        {
            Console.WriteLine($"{param.Key}: {param.Value}");
        }
    }

    /// <summary>
    /// 示例3：组合AND和OR逻辑
    /// </summary>
    public void AndOrLogicExample()
    {
        var whereItems = new List<WhereItem>
        {
            // 基础条件：status = 1
            WhereItemFactory.Equal("status", 1),
            
            // OR条件组：(name LIKE '%admin%' OR email LIKE '%admin%')
            WhereItemFactory.Or("name", WhereOperator.Contains, "admin"),
            WhereItemFactory.Or("email", WhereOperator.Contains, "admin"),
            
            // AND条件：created_time >= ?
            WhereItemFactory.GreaterThanOrEqual("created_time", DateTime.Now.AddDays(-7)),
            
            // OR条件：role_id IN (1,2,3) OR is_super_admin = 1
            WhereItemFactory.Or("role_id", WhereOperator.In, new[] { 1, 2, 3 }),
            WhereItemFactory.Or("is_super_admin", WhereOperator.Equal, true)
        };

        var whereClause = SqlQueryBuilderExtensions.BuildWhereClause(whereItems, out var parameters);

        Console.WriteLine("AND/OR逻辑组合示例:");
        Console.WriteLine(whereClause);
        Console.WriteLine("参数:");
        foreach (var param in parameters)
        {
            Console.WriteLine($"{param.Key}: {param.Value}");
        }
    }

    /// <summary>
    /// 示例4：EXISTS和NOT EXISTS子查询
    /// </summary>
    public void ExistsSubQueryExample()
    {
        var whereItems = new List<WhereItem>
        {
            // 基础条件
            WhereItemFactory.Equal("is_active", true),
            
            // EXISTS：用户必须有角色
            WhereItemFactory.Exists(@"
                SELECT 1 FROM user_roles ur 
                WHERE ur.user_id = users.id 
                    AND ur.is_active = 1"),
            
            // NOT EXISTS：用户没有被锁定的记录
            WhereItemFactory.NotExists(@"
                SELECT 1 FROM user_locks ul 
                WHERE ul.user_id = users.id 
                    AND ul.lock_time > NOW() - INTERVAL 1 HOUR"),
            
            // 复杂的EXISTS：用户在特定项目中有权限
            WhereItemFactory.Exists(@"
                SELECT 1 FROM user_project_permissions upp
                INNER JOIN projects p ON upp.project_id = p.id
                WHERE upp.user_id = users.id 
                    AND p.status = 'active'
                    AND upp.permission_level >= 2")
        };

        var whereClause = SqlQueryBuilderExtensions.BuildWhereClause(whereItems, out var parameters);

        Console.WriteLine("EXISTS/NOT EXISTS子查询示例:");
        Console.WriteLine(whereClause);
    }

    /// <summary>
    /// 示例5：NULL值处理
    /// </summary>
    public void NullHandlingExample()
    {
        var whereItems = new List<WhereItem>
        {
            // 字段不为空
            WhereItemFactory.IsNotNull("email"),
            
            // 字段为空
            WhereItemFactory.IsNull("deleted_at"),
            
            // 复杂的NULL处理
            WhereItemFactory.RawSql("(phone IS NOT NULL AND phone != '')"),
            
            // COALESCE处理
            WhereItemFactory.RawSql("COALESCE(display_name, username, email) LIKE {0}", "%admin%")
        };

        var whereClause = SqlQueryBuilderExtensions.BuildWhereClause(whereItems, out var parameters);

        Console.WriteLine("NULL值处理示例:");
        Console.WriteLine(whereClause);
        Console.WriteLine("参数:");
        foreach (var param in parameters)
        {
            Console.WriteLine($"{param.Key}: {param.Value}");
        }
    }

    /// <summary>
    /// 示例6：模拟您的FormInstanceRepository场景的完整实现
    /// </summary>
    public void FormInstanceCompleteExample()
    {
        // 1. 基础查询条件
        var whereItems = new List<WhereItem>
        {
            WhereItemFactory.Equal("is_obsoleted", false),
            WhereItemFactory.IsNotNull("business_id")
        };

        // 2. 添加业务逻辑条件（如果有的话）
        // 比如：特定的表单类型
        whereItems.Add(WhereItemFactory.Equal("form_type", "dynamic"));

        // 3. 添加复杂的版本控制逻辑：只获取每个business_id的最新版本
        var latestVersionSubQuery = @"
            SELECT 1 FROM form_instances f_inner 
            WHERE f_inner.key != form_instances.key 
                AND f_inner.is_obsoleted = 0 
                AND LOWER(f_inner.business_id) = LOWER(form_instances.business_id)
                AND f_inner.version_time > form_instances.version_time";

        whereItems.Add(WhereItemFactory.NotExists(latestVersionSubQuery));

        // 4. 可选：添加时间范围限制
        whereItems.Add(WhereItemFactory.GreaterThanOrEqual("created_time", DateTime.Now.AddMonths(-6)));

        // 5. 构建WHERE条件
        var whereClause = SqlQueryBuilderExtensions.BuildWhereClause(whereItems, out var parameters);

        // 6. 构建ORDER BY条件
        var orderByItems = new List<OrderByItem>
        {
            OrderByItemFactory.Descending("version_time"),
            OrderByItemFactory.Ascending("business_id")
        };

        var orderByClause = SqlQueryBuilderExtensions.BuildOrderByClause(orderByItems);

        // 7. 组合完整查询
        var baseQuery = @"
            SELECT 
                fi.id,
                fi.key,
                fi.business_id,
                fi.form_type,
                fi.version_time,
                fi.created_time,
                fi.metadata
            FROM form_instances fi";

        var completeSql = $"{baseQuery} {whereClause} {orderByClause}";

        Console.WriteLine("FormInstance完整查询示例:");
        Console.WriteLine(completeSql);
        Console.WriteLine("\n参数:");
        foreach (var param in parameters)
        {
            Console.WriteLine($"{param.Key}: {param.Value}");
        }
    }
}
