using System.ComponentModel.DataAnnotations.Schema;
using XJ.Framework.Library.Application.Contract.Extensions;
using XJ.Framework.Library.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Application.Contract.QueryCriteria.Attributes;
using XJ.Framework.Library.Domain.Entities;

namespace XJ.Framework.Library.Application.Contract.Examples;

/// <summary>
/// SQL查询构建器使用示例
/// </summary>
public class SqlQueryBuilderExample
{
    /// <summary>
    /// 示例实体
    /// </summary>
    public class UserEntity : BaseEntity<long>
    {
        [Column("username")]
        public string Username { get; set; } = string.Empty;

        [Column("email")]
        public string Email { get; set; } = string.Empty;

        [Column("real_name")]
        public string RealName { get; set; } = string.Empty;

        [Column("status")]
        public int Status { get; set; }

        [Column("created_time")]
        public DateTime CreatedTime { get; set; }
    }

    /// <summary>
    /// 示例查询条件
    /// </summary>
    public class UserQueryCriteria : BaseQueryCriteria
    {
        [Equal]
        public string? Username { get; set; }

        [Equal]
        public string? Email { get; set; }

        [Contains]
        public string? RealName { get; set; }

        [Equal]
        public int? Status { get; set; }

        [GreaterThanOrEqual]
        public DateTime? CreatedTimeStart { get; set; }

        [LessThanOrEqual]
        public DateTime? CreatedTimeEnd { get; set; }
    }

    /// <summary>
    /// 基本使用示例
    /// </summary>
    public void BasicUsageExample()
    {
        // 创建查询条件
        var criteria = new PagedQueryCriteria<UserQueryCriteria>
        {
            Condition = new UserQueryCriteria
            {
                Username = "admin",
                RealName = "管理员",
                Status = 1,
                CreatedTimeStart = DateTime.Now.AddDays(-30)
            },
            PageParams = new PageRequestParams(1, 10),
            OrderBy = new[]
            {
                new OrderByRequestItem("CreatedTime", FieldSortDirection.Descending),
                new OrderByRequestItem("Username", FieldSortDirection.Ascending)
            }
        };

        // 构建WHERE条件
        var whereClause = criteria.BuildWhereClause<long, UserEntity, UserQueryCriteria>(out var parameters);
        Console.WriteLine("WHERE条件:");
        Console.WriteLine(whereClause);
        Console.WriteLine("参数:");
        foreach (var param in parameters)
        {
            Console.WriteLine($"{param.Key}: {param.Value}");
        }

        // 构建ORDER BY条件
        var orderByClause = criteria.BuildOrderByClause<long, UserEntity, UserQueryCriteria>();
        Console.WriteLine("\nORDER BY条件:");
        Console.WriteLine(orderByClause);

        // 构建WHERE和ORDER BY条件
        var combinedClause = criteria.BuildWhereAndOrderByClause<long, UserEntity, UserQueryCriteria>(
            out var combinedParameters);
        Console.WriteLine("\n组合条件:");
        Console.WriteLine(combinedClause);
    }

    /// <summary>
    /// 在Repository中的实际使用示例
    /// </summary>
    public async Task<List<UserEntity>> GetUsersAsync(PagedQueryCriteria<UserQueryCriteria> criteria)
    {
        // 基础查询SQL
        var baseQuery = @"
            SELECT u.id, u.username, u.email, u.real_name, u.status, u.created_time
            FROM users u
            LEFT JOIN user_roles ur ON u.id = ur.user_id
            LEFT JOIN roles r ON ur.role_id = r.id";

        // 构建WHERE和ORDER BY条件
        var conditions = criteria.BuildWhereAndOrderByClause<long, UserEntity, UserQueryCriteria>(
            out var parameters);

        // 组合完整SQL
        var completeSql = baseQuery;
        if (!string.IsNullOrEmpty(conditions))
        {
            completeSql += $" {conditions}";
        }

        // 添加分页
        if (criteria.PageParams != null)
        {
            var offset = criteria.PageParams.ToRowIndex();
            var pageSize = criteria.PageParams.PageSize;
            
            parameters["@offset"] = offset;
            parameters["@pageSize"] = pageSize;
            
            completeSql += " OFFSET @offset ROWS FETCH NEXT @pageSize ROWS ONLY";
        }

        Console.WriteLine("完整SQL:");
        Console.WriteLine(completeSql);
        Console.WriteLine("参数:");
        foreach (var param in parameters)
        {
            Console.WriteLine($"{param.Key}: {param.Value}");
        }

        // 执行查询（这里只是示例，实际需要使用DbConnection）
        // using var connection = GetConnection();
        // using var command = connection.CreateCommand();
        // command.CommandText = completeSql;
        // 
        // foreach (var param in parameters)
        // {
        //     var parameter = command.CreateParameter();
        //     parameter.ParameterName = param.Key;
        //     parameter.Value = param.Value;
        //     command.Parameters.Add(parameter);
        // }
        // 
        // var result = await command.ExecuteReaderAsync();
        // return MapToEntities(result);

        return new List<UserEntity>(); // 示例返回
    }

    /// <summary>
    /// 获取用户总数示例
    /// </summary>
    public async Task<int> GetUserCountAsync(PagedQueryCriteria<UserQueryCriteria> criteria)
    {
        var baseCountQuery = "SELECT COUNT(*) FROM users u";

        // 只构建WHERE条件，不需要ORDER BY
        var whereClause = criteria.BuildWhereClause<long, UserEntity, UserQueryCriteria>(out var parameters);

        var completeSql = baseCountQuery;
        if (!string.IsNullOrEmpty(whereClause))
        {
            completeSql += $" {whereClause}";
        }

        Console.WriteLine("计数SQL:");
        Console.WriteLine(completeSql);

        // 执行计数查询
        // using var connection = GetConnection();
        // using var command = connection.CreateCommand();
        // command.CommandText = completeSql;
        // 
        // foreach (var param in parameters)
        // {
        //     var parameter = command.CreateParameter();
        //     parameter.ParameterName = param.Key;
        //     parameter.Value = param.Value;
        //     command.Parameters.Add(parameter);
        // }
        // 
        // return (int)await command.ExecuteScalarAsync();

        return 0; // 示例返回
    }

    /// <summary>
    /// 复杂查询示例
    /// </summary>
    public void ComplexQueryExample()
    {
        var criteria = new PagedQueryCriteria<UserQueryCriteria>
        {
            Condition = new UserQueryCriteria
            {
                RealName = "张",
                Status = 1
            },
            OrderBy = new[]
            {
                new OrderByRequestItem("Status", FieldSortDirection.Ascending),
                new OrderByRequestItem("CreatedTime", FieldSortDirection.Descending)
            }
        };

        // 复杂的联表查询基础SQL
        var complexBaseQuery = @"
            SELECT DISTINCT 
                u.id, 
                u.username, 
                u.email, 
                u.real_name, 
                u.status, 
                u.created_time,
                STRING_AGG(r.name, ',') as role_names,
                COUNT(p.id) as permission_count
            FROM users u
            LEFT JOIN user_roles ur ON u.id = ur.user_id
            LEFT JOIN roles r ON ur.role_id = r.id
            LEFT JOIN role_permissions rp ON r.id = rp.role_id
            LEFT JOIN permissions p ON rp.permission_id = p.id
            GROUP BY u.id, u.username, u.email, u.real_name, u.status, u.created_time";

        // 构建条件
        var conditions = criteria.BuildWhereAndOrderByClause<long, UserEntity, UserQueryCriteria>(
            out var parameters);

        var completeSql = complexBaseQuery;
        if (!string.IsNullOrEmpty(conditions))
        {
            completeSql += $" {conditions}";
        }

        Console.WriteLine("复杂查询SQL:");
        Console.WriteLine(completeSql);
        Console.WriteLine("参数:");
        foreach (var param in parameters)
        {
            Console.WriteLine($"{param.Key}: {param.Value}");
        }
    }
}
