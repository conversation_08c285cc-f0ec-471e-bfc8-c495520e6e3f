using System.ComponentModel.DataAnnotations.Schema;
using System.Reflection;
using XJ.Framework.Library.Application.Contract.QueryCriteria.Attributes;
using XJ.Framework.Library.Domain.Entities;

namespace XJ.Framework.Library.Application.Contract.Extensions;

/// <summary>
/// 查询条件转换为WhereItem的扩展方法
/// </summary>
public static class QueryCriteriaToWhereItemExtensions
{
    /// <summary>
    /// 将PagedQueryCriteria转换为SqlWhereItem列表
    /// </summary>
    /// <typeparam name="TKey">实体主键类型</typeparam>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <typeparam name="TQueryCriteria">查询条件类型</typeparam>
    /// <param name="criteria">分页查询条件</param>
    /// <returns>SqlWhereItem列表</returns>
    public static List<SqlWhereItem> ToSqlWhereItems<TKey, TEntity, TQueryCriteria>(
        this PagedQueryCriteria<TQueryCriteria> criteria)
        where TKey : struct
        where TEntity : BaseEntity<TKey>
        where TQueryCriteria : BaseQueryCriteria
    {
        if (criteria?.Condition == null)
            return new List<SqlWhereItem>();

        var whereItems = new List<SqlWhereItem>();
        var properties = typeof(TQueryCriteria).GetProperties();

        foreach (var property in properties)
        {
            var queryOperator = property.GetCustomAttributes(typeof(QueryOperatorAttribute), true)
                .FirstOrDefault() as QueryOperatorAttribute;

            if (queryOperator == null) continue;

            var propertyValue = property.GetValue(criteria.Condition);
            if (propertyValue == null) continue;

            var propertyName = queryOperator.PropertyName ?? property.Name;
            
            // 获取实体属性信息以确定数据库字段名和类型
            var entityProperty = typeof(TEntity).GetProperty(propertyName);
            if (entityProperty == null)
                throw new InvalidOperationException(
                    $"Property '{propertyName}' not found on type '{typeof(TEntity).Name}'.");

            var dataField = GetDatabaseColumnName(entityProperty);
            var fieldType = entityProperty.PropertyType;

            var whereItem = new SqlWhereItem
            {
                DataField = dataField,
                Operator = ConvertToSqlWhereOperator(queryOperator.Operator),
                Value = propertyValue,
                FieldType = fieldType
            };

            whereItems.Add(whereItem);
        }

        return whereItems;
    }

    /// <summary>
    /// 将PagedQueryCriteria的OrderBy转换为SqlOrderByItem列表
    /// </summary>
    /// <typeparam name="TKey">实体主键类型</typeparam>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <typeparam name="TQueryCriteria">查询条件类型</typeparam>
    /// <param name="criteria">分页查询条件</param>
    /// <returns>SqlOrderByItem列表</returns>
    public static List<SqlOrderByItem> ToSqlOrderByItems<TKey, TEntity, TQueryCriteria>(
        this PagedQueryCriteria<TQueryCriteria> criteria)
        where TKey : struct
        where TEntity : BaseEntity<TKey>
        where TQueryCriteria : BaseQueryCriteria
    {
        if (criteria?.OrderBy == null || !criteria.OrderBy.Any())
            return new List<SqlOrderByItem>();

        var orderByItems = new List<SqlOrderByItem>();

        foreach (var orderByRequestItem in criteria.OrderBy)
        {
            var propertyName = CapitalizeFirstLetter(orderByRequestItem.DataField);
            
            // 获取实体属性信息以确定数据库字段名
            var entityProperty = typeof(TEntity).GetProperty(propertyName);
            if (entityProperty == null)
                throw new InvalidOperationException(
                    $"Property '{propertyName}' not found on type '{typeof(TEntity).Name}'.");

            var dataField = GetDatabaseColumnName(entityProperty);

            var orderByItem = new SqlOrderByItem
            {
                DataField = dataField,
                SortDirection = ConvertToSqlOrderByDirection(orderByRequestItem.SortDirection)
            };

            orderByItems.Add(orderByItem);
        }

        return orderByItems;
    }

    /// <summary>
    /// 获取数据库列名
    /// </summary>
    /// <param name="propertyInfo">属性信息</param>
    /// <returns>数据库列名</returns>
    private static string GetDatabaseColumnName(PropertyInfo propertyInfo)
    {
        var columnAttribute = propertyInfo.GetCustomAttribute<ColumnAttribute>();
        return columnAttribute?.Name ?? propertyInfo.Name.ToLower();
    }

    /// <summary>
    /// 将首字母大写
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>首字母大写的字符串</returns>
    private static string CapitalizeFirstLetter(string input)
    {
        if (string.IsNullOrEmpty(input)) return input;

        if (char.IsLower(input[0]))
        {
            return char.ToUpper(input[0]) + input.Substring(1);
        }

        return input;
    }

    /// <summary>
    /// 转换查询操作符
    /// </summary>
    /// <param name="queryOperator">查询操作符</param>
    /// <returns>SqlWhereOperator</returns>
    private static SqlWhereOperator ConvertToSqlWhereOperator(QueryOperator queryOperator)
    {
        return queryOperator switch
        {
            QueryOperator.Equal => WhereOperatorDto.Equal,
            QueryOperator.NotEqual => WhereOperatorDto.NotEqual,
            QueryOperator.Contains => WhereOperatorDto.Contains,
            QueryOperator.GreaterThan => WhereOperatorDto.GreaterThan,
            QueryOperator.GreaterThanOrEqual => WhereOperatorDto.GreaterThanOrEqual,
            QueryOperator.LessThan => WhereOperatorDto.LessThan,
            QueryOperator.LessThanOrEqual => WhereOperatorDto.LessThanOrEqual,
            QueryOperator.StartsWith => WhereOperatorDto.StartsWith,
            QueryOperator.EndsWith => WhereOperatorDto.EndsWith,
            QueryOperator.Between => WhereOperatorDto.Between,
            QueryOperator.In => WhereOperatorDto.In,
            _ => throw new NotSupportedException($"Operator {queryOperator} is not supported")
        };
    }

    /// <summary>
    /// 转换排序方向
    /// </summary>
    /// <param name="sortDirection">排序方向</param>
    /// <returns>OrderByDirection</returns>
    private static OrderByDirectionDto ConvertToOrderByDirection(FieldSortDirection sortDirection)
    {
        return sortDirection switch
        {
            FieldSortDirection.Ascending => OrderByDirectionDto.Ascending,
            FieldSortDirection.Descending => OrderByDirectionDto.Descending,
            _ => throw new NotSupportedException($"Sort direction {sortDirection} is not supported")
        };
    }
}

/// <summary>
/// WHERE条件项DTO
/// </summary>
public class WhereItemDto
{
    /// <summary>
    /// 数据库字段名
    /// </summary>
    public string DataField { get; set; } = string.Empty;

    /// <summary>
    /// 查询操作符
    /// </summary>
    public WhereOperatorDto Operator { get; set; }

    /// <summary>
    /// 查询值
    /// </summary>
    public object? Value { get; set; }

    /// <summary>
    /// 字段的数据类型
    /// </summary>
    public Type? FieldType { get; set; }
}

/// <summary>
/// ORDER BY条件项DTO
/// </summary>
public class OrderByItemDto
{
    /// <summary>
    /// 数据库字段名
    /// </summary>
    public string DataField { get; set; } = string.Empty;

    /// <summary>
    /// 排序方向
    /// </summary>
    public OrderByDirectionDto SortDirection { get; set; }
}

/// <summary>
/// WHERE操作符枚举DTO
/// </summary>
public enum WhereOperatorDto
{
    Equal,
    NotEqual,
    Contains,
    GreaterThan,
    GreaterThanOrEqual,
    LessThan,
    LessThanOrEqual,
    StartsWith,
    EndsWith,
    Between,
    In
}

/// <summary>
/// 排序方向枚举DTO
/// </summary>
public enum OrderByDirectionDto
{
    Ascending,
    Descending
}
