using XJ.Framework.Library.Domain.Entities;
using XJ.Framework.Library.EntityFrameworkCore.QueryBuilders;
using XJ.Framework.Library.EntityFrameworkCore.QueryCriteria;

namespace XJ.Framework.Library.Application.Contract.Extensions;

/// <summary>
/// SQL查询构建器扩展方法
/// </summary>
public static class SqlQueryBuilderExtensions
{
    #region 获取条件项集合的方法

    /// <summary>
    /// 获取WHERE条件项集合
    /// </summary>
    /// <typeparam name="TKey">实体主键类型</typeparam>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <typeparam name="TQueryCriteria">查询条件类型</typeparam>
    /// <param name="criteria">分页查询条件</param>
    /// <returns>WhereItem集合</returns>
    public static List<WhereItem> GetWhereItems<TKey, TEntity, TQueryCriteria>(
        this PagedQueryCriteria<TQueryCriteria> criteria)
        where TKey : struct
        where TEntity : BaseEntity<TKey>
        where TQueryCriteria : BaseQueryCriteria
    {
        return criteria.ToWhereItems<TKey, TEntity, TQueryCriteria>();
    }

    /// <summary>
    /// 获取ORDER BY条件项集合
    /// </summary>
    /// <typeparam name="TKey">实体主键类型</typeparam>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <typeparam name="TQueryCriteria">查询条件类型</typeparam>
    /// <param name="criteria">分页查询条件</param>
    /// <returns>OrderByItem集合</returns>
    public static List<OrderByItem> GetOrderByItems<TKey, TEntity, TQueryCriteria>(
        this PagedQueryCriteria<TQueryCriteria> criteria)
        where TKey : struct
        where TEntity : BaseEntity<TKey>
        where TQueryCriteria : BaseQueryCriteria
    {
        return criteria.ToOrderByItems<TKey, TEntity, TQueryCriteria>();
    }

    #endregion

    #region 直接构建SQL字符串的方法

    /// <summary>
    /// 构建WHERE条件SQL
    /// </summary>
    /// <typeparam name="TKey">实体主键类型</typeparam>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <typeparam name="TQueryCriteria">查询条件类型</typeparam>
    /// <param name="criteria">分页查询条件</param>
    /// <param name="parameters">SQL参数字典</param>
    /// <returns>WHERE条件SQL字符串</returns>
    public static string BuildWhereClause<TKey, TEntity, TQueryCriteria>(
        this PagedQueryCriteria<TQueryCriteria> criteria,
        out Dictionary<string, object> parameters)
        where TKey : struct
        where TEntity : BaseEntity<TKey>
        where TQueryCriteria : BaseQueryCriteria
    {
        parameters = new Dictionary<string, object>();

        // 转换为EntityFrameworkCore层的WhereItem
        var whereItems = criteria.ToWhereItems<TKey, TEntity, TQueryCriteria>();

        // 使用EntityFrameworkCore层的构建器生成SQL
        return WhereItemBuilder.BuildWhereClause(whereItems, parameters);
    }

    /// <summary>
    /// 构建ORDER BY条件SQL
    /// </summary>
    /// <typeparam name="TKey">实体主键类型</typeparam>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <typeparam name="TQueryCriteria">查询条件类型</typeparam>
    /// <param name="criteria">分页查询条件</param>
    /// <param name="defaultOrderBy">默认排序字段</param>
    /// <returns>ORDER BY条件SQL字符串</returns>
    public static string BuildOrderByClause<TKey, TEntity, TQueryCriteria>(
        this PagedQueryCriteria<TQueryCriteria> criteria,
        string defaultOrderBy = "id")
        where TKey : struct
        where TEntity : BaseEntity<TKey>
        where TQueryCriteria : BaseQueryCriteria
    {
        // 转换为EntityFrameworkCore层的OrderByItem
        var orderByItems = criteria.ToOrderByItems<TKey, TEntity, TQueryCriteria>();

        // 使用EntityFrameworkCore层的构建器生成SQL
        return OrderByItemBuilder.BuildOrderByClause(orderByItems, defaultOrderBy);
    }

    #endregion

    /// <summary>
    /// 构建WHERE和ORDER BY条件SQL
    /// </summary>
    /// <typeparam name="TKey">实体主键类型</typeparam>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <typeparam name="TQueryCriteria">查询条件类型</typeparam>
    /// <param name="criteria">分页查询条件</param>
    /// <param name="parameters">SQL参数字典</param>
    /// <param name="defaultOrderBy">默认排序字段</param>
    /// <returns>WHERE和ORDER BY条件SQL字符串</returns>
    public static string BuildWhereAndOrderByClause<TKey, TEntity, TQueryCriteria>(
        this PagedQueryCriteria<TQueryCriteria> criteria,
        out Dictionary<string, object> parameters,
        string defaultOrderBy = "id")
        where TKey : struct
        where TEntity : BaseEntity<TKey>
        where TQueryCriteria : BaseQueryCriteria
    {
        var whereClause = criteria.BuildWhereClause<TKey, TEntity, TQueryCriteria>(out parameters);
        var orderByClause = criteria.BuildOrderByClause<TKey, TEntity, TQueryCriteria>(defaultOrderBy);

        var result = new List<string>();
        
        if (!string.IsNullOrEmpty(whereClause))
            result.Add(whereClause);
            
        if (!string.IsNullOrEmpty(orderByClause))
            result.Add(orderByClause);

        return string.Join(" ", result);
    }
}
