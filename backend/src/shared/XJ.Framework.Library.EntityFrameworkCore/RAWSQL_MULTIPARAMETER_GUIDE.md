# RawSql多参数支持指南

## 概述

`WhereOperator.RawSql`现在支持多个参数，解决了复杂SQL条件需要多个参数值的问题。

## 支持的使用方式

### 1. 单个参数（向后兼容）
```csharp
// 方式1：使用单参数重载
WhereItemFactory.RawSql("LOWER(business_id) = LOWER({0})", "FORM_001")

// 方式2：使用params重载（单个参数）
WhereItemFactory.RawSql("user_id = {0}", 123)
```

### 2. 多个参数（新功能）
```csharp
// 多参数的日期范围查询
WhereItemFactory.RawSql("created_time BETWEEN {0} AND {1}", 
    DateTime.Now.AddDays(-30), DateTime.Now)

// 多参数的复杂条件
WhereItemFactory.RawSql("(priority BETWEEN {0} AND {1}) OR (urgent = {2})", 
    1, 5, true)

// 多参数的字符串匹配
WhereItemFactory.RawSql("(name LIKE {0} OR description LIKE {1}) AND category = {2}",
    "%admin%", "%管理%", "system")
```

### 3. 无参数
```csharp
// 无参数的原生SQL
WhereItemFactory.RawSqlNoParams("deleted_at IS NULL")
WhereItemFactory.RawSqlNoParams("YEAR(created_time) = YEAR(NOW())")
```

## 参数占位符规则

- 使用`{0}`, `{1}`, `{2}`...作为参数占位符
- 占位符索引从0开始，按顺序递增
- 参数数量必须与占位符数量匹配

## 实际使用示例

### 示例1：复杂的日期和状态查询
```csharp
var whereItems = new List<WhereItem>
{
    WhereItemFactory.Equal("is_active", true),
    
    // 多参数：日期范围 + 状态检查
    WhereItemFactory.RawSql(
        "created_time BETWEEN {0} AND {1} AND updated_time > {2}",
        DateTime.Now.AddDays(-30),  // {0}
        DateTime.Now,               // {1}
        DateTime.Now.AddDays(-7)    // {2}
    )
};
```

### 示例2：JSON字段的复杂查询
```csharp
var whereItems = new List<WhereItem>
{
    // 多个JSON字段的查询
    WhereItemFactory.RawSql(
        "JSON_EXTRACT(settings, '$.enabled') = {0} AND JSON_EXTRACT(metadata, '$.version') >= {1}",
        true,    // {0}
        "2.0"    // {1}
    ),
    
    // JSON数组查询
    WhereItemFactory.RawSql(
        "JSON_CONTAINS(tags, {0}) OR JSON_EXTRACT(config, '$.priority') >= {1}",
        "\"important\"",  // {0} - JSON字符串需要转义
        5                 // {1}
    )
};
```

### 示例3：数值范围和集合查询
```csharp
var whereItems = new List<WhereItem>
{
    // 多参数的数值范围和状态检查
    WhereItemFactory.RawSql(
        "score BETWEEN {0} AND {1} AND status IN ({2}, {3}, {4})",
        80,         // {0}
        100,        // {1}
        "active",   // {2}
        "pending",  // {3}
        "approved"  // {4}
    )
};
```

### 示例4：字符串处理和模糊匹配
```csharp
var whereItems = new List<WhereItem>
{
    // 多参数的字符串处理
    WhereItemFactory.RawSql(
        "(LOWER(name) LIKE LOWER({0}) OR LOWER(description) LIKE LOWER({1})) AND category = {2}",
        "%admin%",   // {0}
        "%管理员%",   // {1}
        "system"     // {2}
    ),
    
    // 复杂的字符串长度和内容检查
    WhereItemFactory.RawSql(
        "LENGTH(title) BETWEEN {0} AND {1} AND title NOT LIKE {2}",
        5,           // {0}
        100,         // {1}
        "%test%"     // {2}
    )
};
```

## 生成的SQL示例

### 输入代码
```csharp
var whereItems = new List<WhereItem>
{
    WhereItemFactory.Equal("status", 1),
    WhereItemFactory.RawSql("created_time BETWEEN {0} AND {1}", 
        DateTime.Parse("2024-01-01"), DateTime.Parse("2024-12-31")),
    WhereItemFactory.RawSql("(priority >= {0} AND category = {1}) OR urgent = {2}", 
        5, "important", true)
};

var whereClause = SqlQueryBuilderExtensions.BuildWhereClause(whereItems, out var parameters);
```

### 生成的SQL
```sql
WHERE status = @p0 
  AND created_time BETWEEN @p1 AND @p2 
  AND (priority >= @p3 AND category = @p4) OR urgent = @p5
```

### 参数字典
```
@p0: 1
@p1: 2024-01-01 00:00:00
@p2: 2024-12-31 00:00:00
@p3: 5
@p4: "important"
@p5: true
```

## 最佳实践

### 1. 参数类型匹配
```csharp
// ✅ 正确：参数类型与数据库字段类型匹配
WhereItemFactory.RawSql("user_id = {0} AND score >= {1}", 123, 85.5)

// ❌ 错误：类型不匹配可能导致SQL错误
WhereItemFactory.RawSql("user_id = {0}", "123") // user_id是int类型
```

### 2. 字符串参数处理
```csharp
// ✅ 正确：让参数化查询处理字符串转义
WhereItemFactory.RawSql("name LIKE {0}", "%O'Connor%")

// ❌ 错误：手动拼接字符串容易导致SQL注入
WhereItemFactory.RawSqlNoParams("name LIKE '%O'Connor%'")
```

### 3. 日期参数处理
```csharp
// ✅ 正确：使用DateTime对象
WhereItemFactory.RawSql("created_time >= {0}", DateTime.Now.AddDays(-7))

// ✅ 也可以：使用日期字符串（但推荐DateTime对象）
WhereItemFactory.RawSql("DATE(created_time) = {0}", "2024-01-01")
```

### 4. NULL值处理
```csharp
// ✅ 正确：使用专门的NULL检查方法
WhereItemFactory.IsNull("deleted_at")
WhereItemFactory.IsNotNull("email")

// ✅ 也可以：使用RawSql处理复杂的NULL逻辑
WhereItemFactory.RawSqlNoParams("(phone IS NOT NULL AND phone != '')")
```

## 注意事项

1. **参数索引**：占位符必须从{0}开始，连续递增
2. **参数数量**：参数数量必须与占位符数量完全匹配
3. **SQL注入防护**：所有参数都会自动进行参数化处理
4. **类型安全**：确保参数类型与数据库字段类型兼容
5. **性能考虑**：复杂的RawSql条件可能影响查询性能，建议适当使用索引

## 错误处理

### 常见错误
```csharp
// ❌ 错误：占位符数量与参数数量不匹配
WhereItemFactory.RawSql("field1 = {0} AND field2 = {1}", "value1") // 缺少第二个参数

// ❌ 错误：占位符索引不连续
WhereItemFactory.RawSql("field1 = {0} AND field2 = {2}", "value1", "value2") // 缺少{1}

// ❌ 错误：空的SQL片段
WhereItemFactory.RawSql("", "value1")
```

### 正确的做法
```csharp
// ✅ 正确：参数数量匹配
WhereItemFactory.RawSql("field1 = {0} AND field2 = {1}", "value1", "value2")

// ✅ 正确：连续的占位符索引
WhereItemFactory.RawSql("field1 = {0} AND field2 = {1} AND field3 = {2}", "v1", "v2", "v3")

// ✅ 正确：有意义的SQL片段
WhereItemFactory.RawSql("COALESCE(display_name, username) LIKE {0}", "%admin%")
```

这个多参数支持完全解决了复杂SQL条件的参数传递问题，同时保持了类型安全和SQL注入防护。
