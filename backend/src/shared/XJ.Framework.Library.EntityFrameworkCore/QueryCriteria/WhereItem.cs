namespace XJ.Framework.Library.EntityFrameworkCore.QueryCriteria;

/// <summary>
/// WHERE条件项
/// </summary>
public class WhereItem
{
    /// <summary>
    /// 数据库字段名
    /// </summary>
    public string DataField { get; set; } = string.Empty;

    /// <summary>
    /// 查询操作符
    /// </summary>
    public WhereOperator Operator { get; set; }

    /// <summary>
    /// 查询值
    /// </summary>
    public object? Value { get; set; }

    /// <summary>
    /// 字段的数据类型
    /// </summary>
    public Type? FieldType { get; set; }

    /// <summary>
    /// 原生SQL片段（当Operator为RawSql时使用）
    /// </summary>
    public string? RawSql { get; set; }

    /// <summary>
    /// 原生SQL参数数组（当Operator为RawSql时使用，支持多个参数）
    /// </summary>
    public object[]? RawSqlParameters { get; set; }

    /// <summary>
    /// 子查询SQL（当Operator为Exists或NotExists时使用）
    /// </summary>
    public string? SubQuery { get; set; }

    /// <summary>
    /// 逻辑连接符（AND/OR），默认为AND
    /// </summary>
    public LogicalOperator LogicalOperator { get; set; } = LogicalOperator.And;
}

/// <summary>
/// 逻辑操作符枚举
/// </summary>
public enum LogicalOperator
{
    /// <summary>
    /// AND连接
    /// </summary>
    And,

    /// <summary>
    /// OR连接
    /// </summary>
    Or
}

/// <summary>
/// WHERE操作符枚举
/// </summary>
public enum WhereOperator
{
    /// <summary>
    /// 等于
    /// </summary>
    Equal,

    /// <summary>
    /// 不等于
    /// </summary>
    NotEqual,

    /// <summary>
    /// 包含（LIKE %value%）
    /// </summary>
    Contains,

    /// <summary>
    /// 大于
    /// </summary>
    GreaterThan,

    /// <summary>
    /// 大于等于
    /// </summary>
    GreaterThanOrEqual,

    /// <summary>
    /// 小于
    /// </summary>
    LessThan,

    /// <summary>
    /// 小于等于
    /// </summary>
    LessThanOrEqual,

    /// <summary>
    /// 开始于（LIKE value%）
    /// </summary>
    StartsWith,

    /// <summary>
    /// 结束于（LIKE %value）
    /// </summary>
    EndsWith,

    /// <summary>
    /// 范围查询（BETWEEN）
    /// </summary>
    Between,

    /// <summary>
    /// 在集合中（IN）
    /// </summary>
    In,

    /// <summary>
    /// 不在集合中（NOT IN）
    /// </summary>
    NotIn,

    /// <summary>
    /// 存在（EXISTS）
    /// </summary>
    Exists,

    /// <summary>
    /// 不存在（NOT EXISTS）
    /// </summary>
    NotExists,

    /// <summary>
    /// 原生SQL片段
    /// </summary>
    RawSql,

    /// <summary>
    /// 为空（IS NULL）
    /// </summary>
    IsNull,

    /// <summary>
    /// 不为空（IS NOT NULL）
    /// </summary>
    IsNotNull
}
