namespace XJ.Framework.Library.EntityFrameworkCore.QueryCriteria;

/// <summary>
/// OrderByItem工厂类，提供便捷的创建方法
/// </summary>
public static class OrderByItemFactory
{
    /// <summary>
    /// 创建升序排序条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <returns>OrderByItem</returns>
    public static OrderByItem Ascending(string dataField)
    {
        return new OrderByItem
        {
            DataField = dataField,
            SortDirection = OrderByDirection.Ascending
        };
    }

    /// <summary>
    /// 创建降序排序条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <returns>OrderByItem</returns>
    public static OrderByItem Descending(string dataField)
    {
        return new OrderByItem
        {
            DataField = dataField,
            SortDirection = OrderByDirection.Descending
        };
    }

    /// <summary>
    /// 创建排序条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="direction">排序方向</param>
    /// <returns>OrderByItem</returns>
    public static OrderByItem Create(string dataField, OrderByDirection direction)
    {
        return new OrderByItem
        {
            DataField = dataField,
            SortDirection = direction
        };
    }

    /// <summary>
    /// 批量创建排序条件
    /// </summary>
    /// <param name="orderBySpecs">排序规格数组，格式：(字段名, 排序方向)</param>
    /// <returns>OrderByItem集合</returns>
    public static List<OrderByItem> CreateMultiple(params (string dataField, OrderByDirection direction)[] orderBySpecs)
    {
        return orderBySpecs.Select(spec => Create(spec.dataField, spec.direction)).ToList();
    }

    /// <summary>
    /// 批量创建升序排序条件
    /// </summary>
    /// <param name="dataFields">数据库字段名数组</param>
    /// <returns>OrderByItem集合</returns>
    public static List<OrderByItem> AscendingMultiple(params string[] dataFields)
    {
        return dataFields.Select(Ascending).ToList();
    }

    /// <summary>
    /// 批量创建降序排序条件
    /// </summary>
    /// <param name="dataFields">数据库字段名数组</param>
    /// <returns>OrderByItem集合</returns>
    public static List<OrderByItem> DescendingMultiple(params string[] dataFields)
    {
        return dataFields.Select(Descending).ToList();
    }
}
