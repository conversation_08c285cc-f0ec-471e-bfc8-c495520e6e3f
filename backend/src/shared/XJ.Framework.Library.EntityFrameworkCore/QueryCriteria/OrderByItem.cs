namespace XJ.Framework.Library.EntityFrameworkCore.QueryCriteria;

/// <summary>
/// ORDER BY条件项
/// </summary>
public class OrderByItem
{
    /// <summary>
    /// 数据库字段名
    /// </summary>
    public string DataField { get; set; } = string.Empty;

    /// <summary>
    /// 排序方向
    /// </summary>
    public OrderByDirection SortDirection { get; set; }
}

/// <summary>
/// 排序方向枚举
/// </summary>
public enum OrderByDirection
{
    /// <summary>
    /// 升序
    /// </summary>
    Ascending,

    /// <summary>
    /// 降序
    /// </summary>
    Descending
}
