namespace XJ.Framework.Library.EntityFrameworkCore.QueryCriteria;

/// <summary>
/// WhereItem工厂类，提供便捷的创建方法
/// </summary>
public static class WhereItemFactory
{
    /// <summary>
    /// 创建等于条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="value">查询值</param>
    /// <returns>WhereItem</returns>
    public static WhereItem Equal(string dataField, object? value)
    {
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.Equal,
            Value = value,
            FieldType = value?.GetType()
        };
    }

    /// <summary>
    /// 创建不等于条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="value">查询值</param>
    /// <returns>WhereItem</returns>
    public static WhereItem NotEqual(string dataField, object? value)
    {
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.NotEqual,
            Value = value,
            FieldType = value?.GetType()
        };
    }

    /// <summary>
    /// 创建包含条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="value">查询值</param>
    /// <returns>WhereItem</returns>
    public static WhereItem Contains(string dataField, string? value)
    {
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.Contains,
            Value = value,
            FieldType = typeof(string)
        };
    }

    /// <summary>
    /// 创建开始于条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="value">查询值</param>
    /// <returns>WhereItem</returns>
    public static WhereItem StartsWith(string dataField, string? value)
    {
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.StartsWith,
            Value = value,
            FieldType = typeof(string)
        };
    }

    /// <summary>
    /// 创建结束于条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="value">查询值</param>
    /// <returns>WhereItem</returns>
    public static WhereItem EndsWith(string dataField, string? value)
    {
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.EndsWith,
            Value = value,
            FieldType = typeof(string)
        };
    }

    /// <summary>
    /// 创建大于条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="value">查询值</param>
    /// <returns>WhereItem</returns>
    public static WhereItem GreaterThan(string dataField, object? value)
    {
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.GreaterThan,
            Value = value,
            FieldType = value?.GetType()
        };
    }

    /// <summary>
    /// 创建大于等于条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="value">查询值</param>
    /// <returns>WhereItem</returns>
    public static WhereItem GreaterThanOrEqual(string dataField, object? value)
    {
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.GreaterThanOrEqual,
            Value = value,
            FieldType = value?.GetType()
        };
    }

    /// <summary>
    /// 创建小于条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="value">查询值</param>
    /// <returns>WhereItem</returns>
    public static WhereItem LessThan(string dataField, object? value)
    {
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.LessThan,
            Value = value,
            FieldType = value?.GetType()
        };
    }

    /// <summary>
    /// 创建小于等于条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="value">查询值</param>
    /// <returns>WhereItem</returns>
    public static WhereItem LessThanOrEqual(string dataField, object? value)
    {
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.LessThanOrEqual,
            Value = value,
            FieldType = value?.GetType()
        };
    }

    /// <summary>
    /// 创建范围条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="lowerValue">下限值</param>
    /// <param name="upperValue">上限值</param>
    /// <returns>WhereItem</returns>
    public static WhereItem Between(string dataField, object? lowerValue, object? upperValue)
    {
        var values = new List<object?> { lowerValue, upperValue };
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.Between,
            Value = values,
            FieldType = lowerValue?.GetType()
        };
    }

    /// <summary>
    /// 创建IN条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="values">值集合</param>
    /// <returns>WhereItem</returns>
    public static WhereItem In(string dataField, IEnumerable<object?> values)
    {
        var valueList = values.ToList();
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.In,
            Value = valueList,
            FieldType = valueList.FirstOrDefault()?.GetType()
        };
    }

    /// <summary>
    /// 创建IN条件（泛型版本）
    /// </summary>
    /// <typeparam name="T">值类型</typeparam>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="values">值集合</param>
    /// <returns>WhereItem</returns>
    public static WhereItem In<T>(string dataField, IEnumerable<T> values)
    {
        var valueList = values.Cast<object?>().ToList();
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.In,
            Value = valueList,
            FieldType = typeof(T)
        };
    }

    /// <summary>
    /// 创建NOT IN条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="values">值集合</param>
    /// <returns>WhereItem</returns>
    public static WhereItem NotIn(string dataField, IEnumerable<object?> values)
    {
        var valueList = values.ToList();
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.NotIn,
            Value = valueList,
            FieldType = valueList.FirstOrDefault()?.GetType()
        };
    }

    /// <summary>
    /// 创建NOT IN条件（泛型版本）
    /// </summary>
    /// <typeparam name="T">值类型</typeparam>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="values">值集合</param>
    /// <returns>WhereItem</returns>
    public static WhereItem NotIn<T>(string dataField, IEnumerable<T> values)
    {
        var valueList = values.Cast<object?>().ToList();
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.NotIn,
            Value = valueList,
            FieldType = typeof(T)
        };
    }

    /// <summary>
    /// 创建EXISTS条件
    /// </summary>
    /// <param name="subQuery">子查询SQL</param>
    /// <returns>WhereItem</returns>
    public static WhereItem Exists(string subQuery)
    {
        return new WhereItem
        {
            DataField = string.Empty,
            Operator = WhereOperator.Exists,
            SubQuery = subQuery
        };
    }

    /// <summary>
    /// 创建NOT EXISTS条件
    /// </summary>
    /// <param name="subQuery">子查询SQL</param>
    /// <returns>WhereItem</returns>
    public static WhereItem NotExists(string subQuery)
    {
        return new WhereItem
        {
            DataField = string.Empty,
            Operator = WhereOperator.NotExists,
            SubQuery = subQuery
        };
    }

    /// <summary>
    /// 创建原生SQL条件（单个参数）
    /// </summary>
    /// <param name="rawSql">原生SQL片段，使用{0}作为参数占位符</param>
    /// <param name="value">参数值（可选）</param>
    /// <returns>WhereItem</returns>
    public static WhereItem RawSql(string rawSql, object? value = null)
    {
        return new WhereItem
        {
            DataField = string.Empty,
            Operator = WhereOperator.RawSql,
            RawSql = rawSql,
            Value = value
        };
    }

    /// <summary>
    /// 创建原生SQL条件（多个参数）
    /// </summary>
    /// <param name="rawSql">原生SQL片段，使用{0}, {1}, {2}...作为参数占位符</param>
    /// <param name="parameters">参数值数组</param>
    /// <returns>WhereItem</returns>
    public static WhereItem RawSql(string rawSql, params object[] parameters)
    {
        return new WhereItem
        {
            DataField = string.Empty,
            Operator = WhereOperator.RawSql,
            RawSql = rawSql,
            RawSqlParameters = parameters
        };
    }

    /// <summary>
    /// 创建原生SQL条件（无参数）
    /// </summary>
    /// <param name="rawSql">原生SQL片段</param>
    /// <returns>WhereItem</returns>
    public static WhereItem RawSqlNoParams(string rawSql)
    {
        return new WhereItem
        {
            DataField = string.Empty,
            Operator = WhereOperator.RawSql,
            RawSql = rawSql
        };
    }

    /// <summary>
    /// 创建IS NULL条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <returns>WhereItem</returns>
    public static WhereItem IsNull(string dataField)
    {
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.IsNull
        };
    }

    /// <summary>
    /// 创建IS NOT NULL条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <returns>WhereItem</returns>
    public static WhereItem IsNotNull(string dataField)
    {
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.IsNotNull
        };
    }

    /// <summary>
    /// 创建OR逻辑连接的条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="operator">操作符</param>
    /// <param name="value">查询值</param>
    /// <returns>WhereItem</returns>
    public static WhereItem Or(string dataField, WhereOperator @operator, object? value)
    {
        return new WhereItem
        {
            DataField = dataField,
            Operator = @operator,
            Value = value,
            FieldType = value?.GetType(),
            LogicalOperator = LogicalOperator.Or
        };
    }
}
