namespace XJ.Framework.Library.EntityFrameworkCore.QueryCriteria;

/// <summary>
/// WhereItem工厂类，提供便捷的创建方法
/// </summary>
public static class WhereItemFactory
{
    /// <summary>
    /// 创建等于条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="value">查询值</param>
    /// <returns>WhereItem</returns>
    public static WhereItem Equal(string dataField, object? value)
    {
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.Equal,
            Value = value,
            FieldType = value?.GetType()
        };
    }

    /// <summary>
    /// 创建不等于条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="value">查询值</param>
    /// <returns>WhereItem</returns>
    public static WhereItem NotEqual(string dataField, object? value)
    {
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.NotEqual,
            Value = value,
            FieldType = value?.GetType()
        };
    }

    /// <summary>
    /// 创建包含条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="value">查询值</param>
    /// <returns>WhereItem</returns>
    public static WhereItem Contains(string dataField, string? value)
    {
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.Contains,
            Value = value,
            FieldType = typeof(string)
        };
    }

    /// <summary>
    /// 创建开始于条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="value">查询值</param>
    /// <returns>WhereItem</returns>
    public static WhereItem StartsWith(string dataField, string? value)
    {
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.StartsWith,
            Value = value,
            FieldType = typeof(string)
        };
    }

    /// <summary>
    /// 创建结束于条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="value">查询值</param>
    /// <returns>WhereItem</returns>
    public static WhereItem EndsWith(string dataField, string? value)
    {
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.EndsWith,
            Value = value,
            FieldType = typeof(string)
        };
    }

    /// <summary>
    /// 创建大于条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="value">查询值</param>
    /// <returns>WhereItem</returns>
    public static WhereItem GreaterThan(string dataField, object? value)
    {
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.GreaterThan,
            Value = value,
            FieldType = value?.GetType()
        };
    }

    /// <summary>
    /// 创建大于等于条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="value">查询值</param>
    /// <returns>WhereItem</returns>
    public static WhereItem GreaterThanOrEqual(string dataField, object? value)
    {
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.GreaterThanOrEqual,
            Value = value,
            FieldType = value?.GetType()
        };
    }

    /// <summary>
    /// 创建小于条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="value">查询值</param>
    /// <returns>WhereItem</returns>
    public static WhereItem LessThan(string dataField, object? value)
    {
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.LessThan,
            Value = value,
            FieldType = value?.GetType()
        };
    }

    /// <summary>
    /// 创建小于等于条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="value">查询值</param>
    /// <returns>WhereItem</returns>
    public static WhereItem LessThanOrEqual(string dataField, object? value)
    {
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.LessThanOrEqual,
            Value = value,
            FieldType = value?.GetType()
        };
    }

    /// <summary>
    /// 创建范围条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="lowerValue">下限值</param>
    /// <param name="upperValue">上限值</param>
    /// <returns>WhereItem</returns>
    public static WhereItem Between(string dataField, object? lowerValue, object? upperValue)
    {
        var values = new List<object?> { lowerValue, upperValue };
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.Between,
            Value = values,
            FieldType = lowerValue?.GetType()
        };
    }

    /// <summary>
    /// 创建IN条件
    /// </summary>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="values">值集合</param>
    /// <returns>WhereItem</returns>
    public static WhereItem In(string dataField, IEnumerable<object?> values)
    {
        var valueList = values.ToList();
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.In,
            Value = valueList,
            FieldType = valueList.FirstOrDefault()?.GetType()
        };
    }

    /// <summary>
    /// 创建IN条件（泛型版本）
    /// </summary>
    /// <typeparam name="T">值类型</typeparam>
    /// <param name="dataField">数据库字段名</param>
    /// <param name="values">值集合</param>
    /// <returns>WhereItem</returns>
    public static WhereItem In<T>(string dataField, IEnumerable<T> values)
    {
        var valueList = values.Cast<object?>().ToList();
        return new WhereItem
        {
            DataField = dataField,
            Operator = WhereOperator.In,
            Value = valueList,
            FieldType = typeof(T)
        };
    }
}
