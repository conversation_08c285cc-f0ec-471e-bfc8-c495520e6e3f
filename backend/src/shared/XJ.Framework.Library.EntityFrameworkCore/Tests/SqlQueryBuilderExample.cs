using XJ.Framework.Library.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Application.Contract.QueryCriteria.Attributes;
using XJ.Framework.Library.Domain.Entities;
using XJ.Framework.Library.EntityFrameworkCore.Extensions;
using System.ComponentModel.DataAnnotations.Schema;

namespace XJ.Framework.Library.EntityFrameworkCore.Tests;

/// <summary>
/// SQL查询构建器使用示例
/// </summary>
public class SqlQueryBuilderExample
{
    /// <summary>
    /// 示例实体
    /// </summary>
    public class UserEntity : BaseEntity<long>
    {
        [Column("username")]
        public string Username { get; set; } = string.Empty;

        [Column("email")]
        public string Email { get; set; } = string.Empty;

        [Column("real_name")]
        public string RealName { get; set; } = string.Empty;

        [Column("status")]
        public int Status { get; set; }

        [Column("created_time")]
        public DateTime CreatedTime { get; set; }
    }

    /// <summary>
    /// 示例查询条件
    /// </summary>
    public class UserQueryCriteria : BaseQueryCriteria
    {
        [Equal]
        public string? Username { get; set; }

        [Equal]
        public string? Email { get; set; }

        [Contains]
        public string? RealName { get; set; }

        [Equal]
        public int? Status { get; set; }

        [GreaterThanOrEqual]
        public DateTime? CreatedTimeStart { get; set; }

        [LessThanOrEqual]
        public DateTime? CreatedTimeEnd { get; set; }
    }

    /// <summary>
    /// 使用示例
    /// </summary>
    public void ExampleUsage()
    {
        // 创建查询条件
        var criteria = new PagedQueryCriteria<UserQueryCriteria>
        {
            Condition = new UserQueryCriteria
            {
                Username = "admin",
                RealName = "管理员",
                Status = 1,
                CreatedTimeStart = DateTime.Now.AddDays(-30)
            },
            PageParams = new PageRequestParams(1, 10),
            OrderBy = new[]
            {
                new OrderByRequestItem("CreatedTime", FieldSortDirection.Descending),
                new OrderByRequestItem("Username", FieldSortDirection.Ascending)
            }
        };

        // 基础查询SQL
        var baseQuery = @"
            SELECT u.id, u.username, u.email, u.real_name, u.status, u.created_time
            FROM users u
            LEFT JOIN user_roles ur ON u.id = ur.user_id
            LEFT JOIN roles r ON ur.role_id = r.id";

        // 构建完整的分页查询
        var pagedSql = criteria.BuildPagedQuery<long, UserEntity, UserQueryCriteria>(
            baseQuery, 
            out var parameters);

        Console.WriteLine("分页查询SQL:");
        Console.WriteLine(pagedSql);
        Console.WriteLine("\n参数:");
        foreach (var param in parameters)
        {
            Console.WriteLine($"{param.Key}: {param.Value}");
        }

        // 构建计数查询
        var baseCountQuery = @"
            SELECT COUNT(DISTINCT u.id)
            FROM users u
            LEFT JOIN user_roles ur ON u.id = ur.user_id
            LEFT JOIN roles r ON ur.role_id = r.id";

        var countSql = criteria.BuildCountQuery<long, UserEntity, UserQueryCriteria>(
            baseCountQuery,
            out var countParameters);

        Console.WriteLine("\n计数查询SQL:");
        Console.WriteLine(countSql);
        Console.WriteLine("\n计数参数:");
        foreach (var param in countParameters)
        {
            Console.WriteLine($"{param.Key}: {param.Value}");
        }

        // 仅构建WHERE条件
        var whereClause = criteria.BuildWhereClause<long, UserEntity, UserQueryCriteria>(
            out var whereParameters);

        Console.WriteLine("\nWHERE条件:");
        Console.WriteLine(whereClause);

        // 仅构建ORDER BY条件
        var orderByClause = criteria.BuildOrderByClause<long, UserEntity, UserQueryCriteria>();

        Console.WriteLine("\nORDER BY条件:");
        Console.WriteLine(orderByClause);
    }

    /// <summary>
    /// 复杂查询示例
    /// </summary>
    public void ComplexQueryExample()
    {
        var criteria = new PagedQueryCriteria<UserQueryCriteria>
        {
            Condition = new UserQueryCriteria
            {
                RealName = "张",
                Status = 1
            },
            PageParams = new PageRequestParams(2, 20),
            OrderBy = new[]
            {
                new OrderByRequestItem("Status", FieldSortDirection.Ascending),
                new OrderByRequestItem("CreatedTime", FieldSortDirection.Descending)
            }
        };

        // 复杂的联表查询
        var complexQuery = @"
            SELECT DISTINCT 
                u.id, 
                u.username, 
                u.email, 
                u.real_name, 
                u.status, 
                u.created_time,
                STRING_AGG(r.name, ',') as role_names,
                COUNT(p.id) as permission_count
            FROM users u
            LEFT JOIN user_roles ur ON u.id = ur.user_id
            LEFT JOIN roles r ON ur.role_id = r.id
            LEFT JOIN role_permissions rp ON r.id = rp.role_id
            LEFT JOIN permissions p ON rp.permission_id = p.id
            GROUP BY u.id, u.username, u.email, u.real_name, u.status, u.created_time";

        var sql = criteria.BuildPagedQuery<long, UserEntity, UserQueryCriteria>(
            complexQuery,
            out var parameters);

        Console.WriteLine("复杂查询SQL:");
        Console.WriteLine(sql);
        Console.WriteLine("\n参数:");
        foreach (var param in parameters)
        {
            Console.WriteLine($"{param.Key}: {param.Value}");
        }
    }

    /// <summary>
    /// 在Repository中的实际使用示例
    /// </summary>
    public async Task<List<UserEntity>> GetUsersAsync(PagedQueryCriteria<UserQueryCriteria> criteria)
    {
        // 基础查询
        var baseQuery = @"
            SELECT u.id, u.username, u.email, u.real_name, u.status, u.created_time
            FROM users u";

        // 构建分页查询SQL
        var sql = criteria.BuildPagedQuery<long, UserEntity, UserQueryCriteria>(
            baseQuery,
            out var parameters);

        // 执行查询（这里只是示例，实际需要使用DbConnection）
        // using var connection = GetConnection();
        // using var command = connection.CreateCommand();
        // command.CommandText = sql;
        // 
        // foreach (var param in parameters)
        // {
        //     var parameter = command.CreateParameter();
        //     parameter.ParameterName = param.Key;
        //     parameter.Value = param.Value;
        //     command.Parameters.Add(parameter);
        // }
        // 
        // var result = await command.ExecuteReaderAsync();
        // return MapToEntities(result);

        return new List<UserEntity>(); // 示例返回
    }

    /// <summary>
    /// 获取用户总数示例
    /// </summary>
    public async Task<int> GetUserCountAsync(PagedQueryCriteria<UserQueryCriteria> criteria)
    {
        var baseCountQuery = "SELECT COUNT(*) FROM users u";

        var sql = criteria.BuildCountQuery<long, UserEntity, UserQueryCriteria>(
            baseCountQuery,
            out var parameters);

        // 执行计数查询
        // using var connection = GetConnection();
        // using var command = connection.CreateCommand();
        // command.CommandText = sql;
        // 
        // foreach (var param in parameters)
        // {
        //     var parameter = command.CreateParameter();
        //     parameter.ParameterName = param.Key;
        //     parameter.Value = param.Value;
        //     command.Parameters.Add(parameter);
        // }
        // 
        // return (int)await command.ExecuteScalarAsync();

        return 0; // 示例返回
    }
}
