using XJ.Framework.Library.Domain.Entities;

namespace XJ.Framework.Library.EntityFrameworkCore.Repositories;

public abstract class BaseAuditRepository<TContext, TKey, TEntity> : BaseEditableRepository<TContext, TKey, TEntity>
    where TContext : BaseDbContext
    where TEntity : BaseAuditEntity<TKey>
    where TKey : struct
{
    public BaseAuditRepository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }


    public async new Task<bool> InsertAsync(TEntity entity, bool isSaveChange = true)
    {
        entity.AuditCreate();
        return await base.InsertAsync(entity, isSaveChange);
    }


    public async new Task<bool> InsertAsync(List<TEntity> entities, bool isSaveChange = true)
    {
        entities.ForEach(entity => entity.AuditCreate());
        return await base.InsertAsync(entities, isSaveChange);
    }


    public async new Task<bool> UpdateAsync(TEntity entity, bool isSaveChange = true,
        List<string>? updatePropertyList = null)
    {
        entity.AuditModify();
        return await base.UpdateAsync(entity, isSaveChange, updatePropertyList);
    }

    public async new Task<bool> UpdateAsync(List<TEntity> entities, bool isSaveChange = true)
    {
        entities.ForEach(entity => entity.AuditModify());
        return await base.UpdateAsync(entities, isSaveChange);
    }
}