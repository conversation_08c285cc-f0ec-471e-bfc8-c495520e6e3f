using XJ.Framework.Library.Application.Contract.Extensions;
using XJ.Framework.Library.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Domain.Entities;
using XJ.Framework.Library.EntityFrameworkCore.QueryBuilders;

namespace XJ.Framework.Library.EntityFrameworkCore.Extensions;

/// <summary>
/// SQL查询构建器扩展方法
/// </summary>
public static class SqlQueryBuilderExtensions
{
    /// <summary>
    /// 构建完整的SQL查询语句
    /// </summary>
    /// <typeparam name="TKey">实体主键类型</typeparam>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <typeparam name="TQueryCriteria">查询条件类型</typeparam>
    /// <param name="criteria">分页查询条件</param>
    /// <param name="baseQuery">基础查询SQL（SELECT ... FROM ...）</param>
    /// <param name="parameters">SQL参数字典</param>
    /// <returns>完整的SQL查询语句</returns>
    public static string BuildCompleteQuery<TKey, TEntity, TQueryCriteria>(
        this PagedQueryCriteria<TQueryCriteria> criteria,
        string baseQuery,
        out Dictionary<string, object> parameters)
        where TKey : struct
        where TEntity : BaseEntity<TKey>
        where TQueryCriteria : BaseQueryCriteria
    {
        parameters = new Dictionary<string, object>();

        // 转换为SQL条件项
        var whereItems = criteria.ToSqlWhereItems<TKey, TEntity, TQueryCriteria>();
        var orderByItems = criteria.ToSqlOrderByItems<TKey, TEntity, TQueryCriteria>();

        // 构建WHERE条件
        var whereClause = WhereItemBuilder.BuildWhereClause(whereItems, parameters);

        // 构建ORDER BY条件
        var orderByClause = OrderByItemBuilder.BuildOrderByClause(orderByItems, "id");

        // 组合完整的SQL
        var completeSql = baseQuery;
        
        if (!string.IsNullOrEmpty(whereClause))
        {
            completeSql += $" {whereClause}";
        }

        if (!string.IsNullOrEmpty(orderByClause))
        {
            completeSql += $" {orderByClause}";
        }

        return completeSql;
    }

    /// <summary>
    /// 构建分页SQL查询语句
    /// </summary>
    /// <typeparam name="TKey">实体主键类型</typeparam>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <typeparam name="TQueryCriteria">查询条件类型</typeparam>
    /// <param name="criteria">分页查询条件</param>
    /// <param name="baseQuery">基础查询SQL（SELECT ... FROM ...）</param>
    /// <param name="parameters">SQL参数字典</param>
    /// <returns>分页SQL查询语句</returns>
    public static string BuildPagedQuery<TKey, TEntity, TQueryCriteria>(
        this PagedQueryCriteria<TQueryCriteria> criteria,
        string baseQuery,
        out Dictionary<string, object> parameters)
        where TKey : struct
        where TEntity : BaseEntity<TKey>
        where TQueryCriteria : BaseQueryCriteria
    {
        var completeSql = criteria.BuildCompleteQuery<TKey, TEntity, TQueryCriteria>(baseQuery, out parameters);

        // 添加分页参数
        if (criteria.PageParams != null)
        {
            var offset = criteria.PageParams.ToRowIndex();
            var pageSize = criteria.PageParams.PageSize;

            parameters["@offset"] = offset;
            parameters["@pageSize"] = pageSize;

            completeSql += " OFFSET @offset ROWS FETCH NEXT @pageSize ROWS ONLY";
        }

        return completeSql;
    }

    /// <summary>
    /// 构建计数SQL查询语句
    /// </summary>
    /// <typeparam name="TKey">实体主键类型</typeparam>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <typeparam name="TQueryCriteria">查询条件类型</typeparam>
    /// <param name="criteria">分页查询条件</param>
    /// <param name="baseCountQuery">基础计数查询SQL（SELECT COUNT(*) FROM ...）</param>
    /// <param name="parameters">SQL参数字典</param>
    /// <returns>计数SQL查询语句</returns>
    public static string BuildCountQuery<TKey, TEntity, TQueryCriteria>(
        this PagedQueryCriteria<TQueryCriteria> criteria,
        string baseCountQuery,
        out Dictionary<string, object> parameters)
        where TKey : struct
        where TEntity : BaseEntity<TKey>
        where TQueryCriteria : BaseQueryCriteria
    {
        parameters = new Dictionary<string, object>();

        // 转换为SQL条件项
        var whereItems = criteria.ToSqlWhereItems<TKey, TEntity, TQueryCriteria>();

        // 构建WHERE条件
        var whereClause = WhereItemBuilder.BuildWhereClause(whereItems, parameters);

        // 组合完整的SQL
        var completeSql = baseCountQuery;
        
        if (!string.IsNullOrEmpty(whereClause))
        {
            completeSql += $" {whereClause}";
        }

        return completeSql;
    }

    /// <summary>
    /// 仅构建WHERE条件SQL
    /// </summary>
    /// <typeparam name="TKey">实体主键类型</typeparam>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <typeparam name="TQueryCriteria">查询条件类型</typeparam>
    /// <param name="criteria">分页查询条件</param>
    /// <param name="parameters">SQL参数字典</param>
    /// <returns>WHERE条件SQL字符串</returns>
    public static string BuildWhereClause<TKey, TEntity, TQueryCriteria>(
        this PagedQueryCriteria<TQueryCriteria> criteria,
        out Dictionary<string, object> parameters)
        where TKey : struct
        where TEntity : BaseEntity<TKey>
        where TQueryCriteria : BaseQueryCriteria
    {
        parameters = new Dictionary<string, object>();

        // 转换为SQL条件项
        var whereItems = criteria.ToSqlWhereItems<TKey, TEntity, TQueryCriteria>();

        // 构建WHERE条件
        return WhereItemBuilder.BuildWhereClause(whereItems, parameters);
    }

    /// <summary>
    /// 仅构建ORDER BY条件SQL
    /// </summary>
    /// <typeparam name="TKey">实体主键类型</typeparam>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <typeparam name="TQueryCriteria">查询条件类型</typeparam>
    /// <param name="criteria">分页查询条件</param>
    /// <param name="defaultOrderBy">默认排序字段</param>
    /// <returns>ORDER BY条件SQL字符串</returns>
    public static string BuildOrderByClause<TKey, TEntity, TQueryCriteria>(
        this PagedQueryCriteria<TQueryCriteria> criteria,
        string defaultOrderBy = "id")
        where TKey : struct
        where TEntity : BaseEntity<TKey>
        where TQueryCriteria : BaseQueryCriteria
    {
        // 转换为SQL条件项
        var orderByItems = criteria.ToSqlOrderByItems<TKey, TEntity, TQueryCriteria>();

        // 构建ORDER BY条件
        return OrderByItemBuilder.BuildOrderByClause(orderByItems, defaultOrderBy);
    }
}
