using XJ.Framework.Library.Application.Contract.Extensions;

namespace XJ.Framework.Library.EntityFrameworkCore.QueryBuilders;

/// <summary>
/// ORDER BY条件SQL构建器
/// </summary>
public static class OrderByItemBuilder
{
    /// <summary>
    /// 构建ORDER BY条件SQL
    /// </summary>
    /// <param name="orderByItems">ORDER BY条件项列表</param>
    /// <returns>ORDER BY条件SQL字符串</returns>
    public static string BuildOrderByClause(List<SqlOrderByItem> orderByItems)
    {
        if (orderByItems == null || !orderByItems.Any())
            return string.Empty;

        var orderByConditions = new List<string>();

        foreach (var orderByItem in orderByItems)
        {
            var direction = orderByItem.SortDirection == SqlOrderByDirection.Ascending ? "ASC" : "DESC";
            orderByConditions.Add($"{orderByItem.DataField} {direction}");
        }

        return orderByConditions.Any() ? $"ORDER BY {string.Join(", ", orderByConditions)}" : string.Empty;
    }

    /// <summary>
    /// 构建ORDER BY条件SQL（带默认排序）
    /// </summary>
    /// <param name="orderByItems">ORDER BY条件项列表</param>
    /// <param name="defaultOrderBy">默认排序字段</param>
    /// <param name="defaultDirection">默认排序方向</param>
    /// <returns>ORDER BY条件SQL字符串</returns>
    public static string BuildOrderByClause(List<SqlOrderByItem> orderByItems, string defaultOrderBy, SqlOrderByDirection defaultDirection = SqlOrderByDirection.Ascending)
    {
        if (orderByItems == null || !orderByItems.Any())
        {
            var direction = defaultDirection == SqlOrderByDirection.Ascending ? "ASC" : "DESC";
            return $"ORDER BY {defaultOrderBy} {direction}";
        }

        return BuildOrderByClause(orderByItems);
    }
}
