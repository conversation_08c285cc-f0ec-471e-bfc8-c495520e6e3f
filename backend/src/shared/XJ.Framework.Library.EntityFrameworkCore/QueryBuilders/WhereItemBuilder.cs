using System.Text;
using XJ.Framework.Library.EntityFrameworkCore.QueryCriteria;

namespace XJ.Framework.Library.EntityFrameworkCore.QueryBuilders;

/// <summary>
/// WHERE条件SQL构建器
/// </summary>
public static class WhereItemBuilder
{
    /// <summary>
    /// 构建WHERE条件SQL
    /// </summary>
    /// <param name="whereItems">WHERE条件项列表</param>
    /// <param name="parameters">SQL参数字典</param>
    /// <returns>WHERE条件SQL字符串</returns>
    public static string BuildWhereClause(List<WhereItem> whereItems, Dictionary<string, object> parameters)
    {
        if (whereItems == null || !whereItems.Any())
            return string.Empty;

        var whereConditions = new List<string>();
        var parameterIndex = 0;

        for (int i = 0; i < whereItems.Count; i++)
        {
            var whereItem = whereItems[i];

            // 对于某些操作符，允许Value为null
            if (whereItem.Value == null && !IsNullableOperator(whereItem.Operator))
                continue;

            var condition = BuildSingleCondition(whereItem, parameters, ref parameterIndex);
            if (!string.IsNullOrEmpty(condition))
            {
                // 处理逻辑连接符
                if (i > 0 && whereConditions.Any())
                {
                    var logicalOp = whereItem.LogicalOperator == LogicalOperator.Or ? " OR " : " AND ";
                    whereConditions.Add($"{logicalOp}{condition}");
                }
                else
                {
                    whereConditions.Add(condition);
                }
            }
        }

        return whereConditions.Any() ? $"WHERE {string.Join("", whereConditions)}" : string.Empty;
    }

    /// <summary>
    /// 检查操作符是否允许Value为null
    /// </summary>
    /// <param name="operator">操作符</param>
    /// <returns>是否允许null值</returns>
    private static bool IsNullableOperator(WhereOperator @operator)
    {
        return @operator == WhereOperator.IsNull ||
               @operator == WhereOperator.IsNotNull ||
               @operator == WhereOperator.RawSql ||
               @operator == WhereOperator.Exists ||
               @operator == WhereOperator.NotExists;
    }

    /// <summary>
    /// 构建单个WHERE条件
    /// </summary>
    /// <param name="whereItem">WHERE条件项</param>
    /// <param name="parameters">SQL参数字典</param>
    /// <param name="parameterIndex">参数索引</param>
    /// <returns>单个WHERE条件SQL</returns>
    private static string BuildSingleCondition(WhereItem whereItem, Dictionary<string, object> parameters, ref int parameterIndex)
    {
        var fieldName = whereItem.DataField;
        var value = whereItem.Value;

        return whereItem.Operator switch
        {
            WhereOperator.Equal => BuildEqualCondition(fieldName, value, parameters, ref parameterIndex),
            WhereOperator.NotEqual => BuildNotEqualCondition(fieldName, value, parameters, ref parameterIndex),
            WhereOperator.Contains => BuildContainsCondition(fieldName, value, parameters, ref parameterIndex),
            WhereOperator.StartsWith => BuildStartsWithCondition(fieldName, value, parameters, ref parameterIndex),
            WhereOperator.EndsWith => BuildEndsWithCondition(fieldName, value, parameters, ref parameterIndex),
            WhereOperator.GreaterThan => BuildGreaterThanCondition(fieldName, value, parameters, ref parameterIndex),
            WhereOperator.GreaterThanOrEqual => BuildGreaterThanOrEqualCondition(fieldName, value, parameters, ref parameterIndex),
            WhereOperator.LessThan => BuildLessThanCondition(fieldName, value, parameters, ref parameterIndex),
            WhereOperator.LessThanOrEqual => BuildLessThanOrEqualCondition(fieldName, value, parameters, ref parameterIndex),
            WhereOperator.Between => BuildBetweenCondition(fieldName, value, parameters, ref parameterIndex),
            WhereOperator.In => BuildInCondition(fieldName, value, parameters, ref parameterIndex),
            WhereOperator.NotIn => BuildNotInCondition(fieldName, value, parameters, ref parameterIndex),
            WhereOperator.Exists => BuildExistsCondition(whereItem.SubQuery),
            WhereOperator.NotExists => BuildNotExistsCondition(whereItem.SubQuery),
            WhereOperator.RawSql => BuildRawSqlCondition(whereItem.RawSql, value, whereItem.RawSqlParameters, parameters, ref parameterIndex),
            WhereOperator.IsNull => BuildIsNullCondition(fieldName),
            WhereOperator.IsNotNull => BuildIsNotNullCondition(fieldName),
            _ => throw new NotSupportedException($"Operator {whereItem.Operator} is not supported")
        };
    }

    /// <summary>
    /// 构建等于条件
    /// </summary>
    private static string BuildEqualCondition(string fieldName, object? value, Dictionary<string, object> parameters, ref int parameterIndex)
    {
        var paramName = $"@p{parameterIndex++}";
        parameters[paramName] = value!;
        return $"{fieldName} = {paramName}";
    }

    /// <summary>
    /// 构建不等于条件
    /// </summary>
    private static string BuildNotEqualCondition(string fieldName, object? value, Dictionary<string, object> parameters, ref int parameterIndex)
    {
        var paramName = $"@p{parameterIndex++}";
        parameters[paramName] = value!;
        return $"{fieldName} <> {paramName}";
    }

    /// <summary>
    /// 构建包含条件
    /// </summary>
    private static string BuildContainsCondition(string fieldName, object? value, Dictionary<string, object> parameters, ref int parameterIndex)
    {
        var paramName = $"@p{parameterIndex++}";
        parameters[paramName] = $"%{value}%";
        return $"{fieldName} LIKE {paramName}";
    }

    /// <summary>
    /// 构建开始于条件
    /// </summary>
    private static string BuildStartsWithCondition(string fieldName, object? value, Dictionary<string, object> parameters, ref int parameterIndex)
    {
        var paramName = $"@p{parameterIndex++}";
        parameters[paramName] = $"{value}%";
        return $"{fieldName} LIKE {paramName}";
    }

    /// <summary>
    /// 构建结束于条件
    /// </summary>
    private static string BuildEndsWithCondition(string fieldName, object? value, Dictionary<string, object> parameters, ref int parameterIndex)
    {
        var paramName = $"@p{parameterIndex++}";
        parameters[paramName] = $"%{value}";
        return $"{fieldName} LIKE {paramName}";
    }

    /// <summary>
    /// 构建大于条件
    /// </summary>
    private static string BuildGreaterThanCondition(string fieldName, object? value, Dictionary<string, object> parameters, ref int parameterIndex)
    {
        var paramName = $"@p{parameterIndex++}";
        parameters[paramName] = value!;
        return $"{fieldName} > {paramName}";
    }

    /// <summary>
    /// 构建大于等于条件
    /// </summary>
    private static string BuildGreaterThanOrEqualCondition(string fieldName, object? value, Dictionary<string, object> parameters, ref int parameterIndex)
    {
        var paramName = $"@p{parameterIndex++}";
        parameters[paramName] = value!;
        return $"{fieldName} >= {paramName}";
    }

    /// <summary>
    /// 构建小于条件
    /// </summary>
    private static string BuildLessThanCondition(string fieldName, object? value, Dictionary<string, object> parameters, ref int parameterIndex)
    {
        var paramName = $"@p{parameterIndex++}";
        parameters[paramName] = value!;
        return $"{fieldName} < {paramName}";
    }

    /// <summary>
    /// 构建小于等于条件
    /// </summary>
    private static string BuildLessThanOrEqualCondition(string fieldName, object? value, Dictionary<string, object> parameters, ref int parameterIndex)
    {
        var paramName = $"@p{parameterIndex++}";
        parameters[paramName] = value!;
        return $"{fieldName} <= {paramName}";
    }

    /// <summary>
    /// 构建BETWEEN条件
    /// </summary>
    private static string BuildBetweenCondition(string fieldName, object? value, Dictionary<string, object> parameters, ref int parameterIndex)
    {
        if (value is not System.Collections.IList list || list.Count != 2)
            throw new ArgumentException("Between operator requires exactly two values");

        var paramName1 = $"@p{parameterIndex++}";
        var paramName2 = $"@p{parameterIndex++}";
        
        parameters[paramName1] = list[0]!;
        parameters[paramName2] = list[1]!;
        
        return $"{fieldName} BETWEEN {paramName1} AND {paramName2}";
    }

    /// <summary>
    /// 构建IN条件
    /// </summary>
    private static string BuildInCondition(string fieldName, object? value, Dictionary<string, object> parameters, ref int parameterIndex)
    {
        if (value is not System.Collections.IEnumerable enumerable)
            throw new ArgumentException("In operator requires an enumerable value");

        var values = enumerable.Cast<object>().ToList();
        if (!values.Any())
            return "1=0"; // 空集合返回永远为false的条件

        var paramNames = new List<string>();
        foreach (var item in values)
        {
            var paramName = $"@p{parameterIndex++}";
            parameters[paramName] = item;
            paramNames.Add(paramName);
        }

        return $"{fieldName} IN ({string.Join(", ", paramNames)})";
    }

    /// <summary>
    /// 构建NOT IN条件
    /// </summary>
    private static string BuildNotInCondition(string fieldName, object? value, Dictionary<string, object> parameters, ref int parameterIndex)
    {
        if (value is not System.Collections.IEnumerable enumerable)
            throw new ArgumentException("NotIn operator requires an enumerable value");

        var values = enumerable.Cast<object>().ToList();
        if (!values.Any())
            return "1=1"; // 空集合返回永远为true的条件

        var paramNames = new List<string>();
        foreach (var item in values)
        {
            var paramName = $"@p{parameterIndex++}";
            parameters[paramName] = item;
            paramNames.Add(paramName);
        }

        return $"{fieldName} NOT IN ({string.Join(", ", paramNames)})";
    }

    /// <summary>
    /// 构建EXISTS条件
    /// </summary>
    private static string BuildExistsCondition(string? subQuery)
    {
        if (string.IsNullOrEmpty(subQuery))
            throw new ArgumentException("Exists operator requires a subquery");

        return $"EXISTS ({subQuery})";
    }

    /// <summary>
    /// 构建NOT EXISTS条件
    /// </summary>
    private static string BuildNotExistsCondition(string? subQuery)
    {
        if (string.IsNullOrEmpty(subQuery))
            throw new ArgumentException("NotExists operator requires a subquery");

        return $"NOT EXISTS ({subQuery})";
    }

    /// <summary>
    /// 构建原生SQL条件
    /// </summary>
    private static string BuildRawSqlCondition(string? rawSql, object? value, Dictionary<string, object> parameters, ref int parameterIndex)
    {
        if (string.IsNullOrEmpty(rawSql))
            throw new ArgumentException("RawSql operator requires a SQL fragment");

        // 如果有参数值，添加到参数字典中
        if (value != null)
        {
            var paramName = $"@p{parameterIndex++}";
            parameters[paramName] = value;
            // 替换SQL中的占位符
            return rawSql.Replace("{0}", paramName);
        }

        return rawSql;
    }

    /// <summary>
    /// 构建IS NULL条件
    /// </summary>
    private static string BuildIsNullCondition(string fieldName)
    {
        return $"{fieldName} IS NULL";
    }

    /// <summary>
    /// 构建IS NOT NULL条件
    /// </summary>
    private static string BuildIsNotNullCondition(string fieldName)
    {
        return $"{fieldName} IS NOT NULL";
    }
}
