# SQL查询构建器使用指南

## 概述

由于EntityFrameworkCore在处理复杂SQL联查操作时的局限性，我们设计了一套分层的解决方案来构建WHERE条件和ORDER BY条件的SQL语句。

## 架构设计

### 分层结构

1. **EntityFrameworkCore层**：
   - `WhereItem` / `OrderByItem` - 包装类（不依赖Application.Contract层）
   - `WhereOperator` / `OrderByDirection` - 操作符枚举
   - `WhereItemBuilder` / `OrderByItemBuilder` - SQL构建器

2. **Application.Contract层**：
   - `PagedQueryCriteria<T>` - 分页查询条件
   - `QueryOperatorAttribute` - 查询操作符属性
   - `QueryCriteriaToWhereItemExtensions` - 转换扩展方法
   - `SqlQueryBuilderExtensions` - 综合扩展方法

### 设计原则

- **分离关注点**：EntityFrameworkCore层处理SQL生成，Application.Contract层处理对象转换
- **避免循环引用**：EntityFrameworkCore层不引用Application.Contract层
- **通用性**：通过泛型和表达式实现最大的可重用性
- **类型安全**：使用反射读取Column属性确保数据库字段名正确
- **功能专一**：只构建WHERE和ORDER BY部分，不构建完整SQL

## 支持的查询操作符

| 操作符 | 属性 | SQL示例 |
|--------|------|---------|
| 等于 | `[Equal]` | `field = @p0` |
| 不等于 | `[NotEqual]` | `field <> @p0` |
| 包含 | `[Contains]` | `field LIKE '%@p0%'` |
| 开始于 | `[StartsWith]` | `field LIKE '@p0%'` |
| 结束于 | `[EndsWith]` | `field LIKE '%@p0'` |
| 大于 | `[GreaterThan]` | `field > @p0` |
| 大于等于 | `[GreaterThanOrEqual]` | `field >= @p0` |
| 小于 | `[LessThan]` | `field < @p0` |
| 小于等于 | `[LessThanOrEqual]` | `field <= @p0` |
| 范围 | `[Between]` | `field BETWEEN @p0 AND @p1` |
| 在集合中 | `[In]` | `field IN (@p0, @p1, @p2)` |

## 使用方法

### 1. 定义实体类

```csharp
public class UserEntity : BaseEntity<long>
{
    [Column("username")]
    public string Username { get; set; } = string.Empty;
    
    [Column("real_name")]
    public string RealName { get; set; } = string.Empty;
    
    [Column("status")]
    public int Status { get; set; }
}
```

### 2. 定义查询条件类

```csharp
public class UserQueryCriteria : BaseQueryCriteria
{
    [Equal]
    public string? Username { get; set; }
    
    [Contains]
    public string? RealName { get; set; }
    
    [Equal]
    public int? Status { get; set; }
}
```

### 3. 使用扩展方法构建SQL

```csharp
var criteria = new PagedQueryCriteria<UserQueryCriteria>
{
    Condition = new UserQueryCriteria
    {
        Username = "admin",
        RealName = "管理员"
    },
    OrderBy = new[]
    {
        new OrderByRequestItem("Username", FieldSortDirection.Ascending)
    }
};

// 构建WHERE条件
var whereClause = criteria.BuildWhereClause<long, UserEntity, UserQueryCriteria>(out var parameters);
// 结果: WHERE username = @p0 AND real_name LIKE @p1

// 构建ORDER BY条件
var orderByClause = criteria.BuildOrderByClause<long, UserEntity, UserQueryCriteria>();
// 结果: ORDER BY username ASC

// 构建组合条件
var combinedClause = criteria.BuildWhereAndOrderByClause<long, UserEntity, UserQueryCriteria>(out var allParameters);
// 结果: WHERE username = @p0 AND real_name LIKE @p1 ORDER BY username ASC
```

### 4. 在Repository中使用

#### 方式1：直接构建SQL字符串
```csharp
public async Task<List<UserEntity>> GetUsersAsync(PagedQueryCriteria<UserQueryCriteria> criteria)
{
    var baseQuery = "SELECT u.id, u.username, u.real_name FROM users u";

    // 构建条件
    var conditions = criteria.BuildWhereAndOrderByClause<long, UserEntity, UserQueryCriteria>(out var parameters);

    // 组合完整SQL
    var completeSql = baseQuery;
    if (!string.IsNullOrEmpty(conditions))
    {
        completeSql += $" {conditions}";
    }

    // 执行查询
    // ... 使用completeSql和parameters执行数据库查询
}
```

#### 方式2：在Application层操作条件项集合
```csharp
public async Task<List<UserEntity>> GetUsersWithExtraConditionsAsync(PagedQueryCriteria<UserQueryCriteria> criteria)
{
    // 1. 获取基础条件项
    var whereItems = criteria.GetWhereItems<long, UserEntity, UserQueryCriteria>();
    var orderByItems = criteria.GetOrderByItems<long, UserEntity, UserQueryCriteria>();

    // 2. 在Application层添加额外的条件
    whereItems.Add(WhereItemFactory.GreaterThanOrEqual("created_time", DateTime.Now.AddDays(-7)));
    orderByItems.Add(OrderByItemFactory.Descending("updated_time"));

    // 3. 构建SQL
    var whereClause = SqlQueryBuilderExtensions.BuildWhereClause(whereItems, out var parameters);
    var orderByClause = SqlQueryBuilderExtensions.BuildOrderByClause(orderByItems);

    var baseQuery = "SELECT u.id, u.username, u.real_name FROM users u";
    var completeSql = $"{baseQuery} {whereClause} {orderByClause}";

    // 执行查询
    // ... 使用completeSql和parameters执行数据库查询
}
```

#### 方式3：使用工厂方法创建条件
```csharp
public async Task<List<UserEntity>> GetActiveUsersAsync()
{
    // 使用工厂方法创建条件
    var whereItems = new List<WhereItem>
    {
        WhereItemFactory.Equal("status", 1),
        WhereItemFactory.GreaterThan("last_login_time", DateTime.Now.AddDays(-30)),
        WhereItemFactory.In("role_id", new[] { 1, 2, 3 })
    };

    var orderByItems = new List<OrderByItem>
    {
        OrderByItemFactory.Descending("last_login_time"),
        OrderByItemFactory.Ascending("username")
    };

    // 构建SQL
    var conditions = SqlQueryBuilderExtensions.BuildWhereAndOrderByClause(
        whereItems, orderByItems, out var parameters);

    var baseQuery = "SELECT * FROM users u";
    var completeSql = $"{baseQuery} {conditions}";

    // 执行查询
    // ... 使用completeSql和parameters执行数据库查询
}
```

## 可用的扩展方法

### Application.Contract层扩展方法

#### 获取条件项集合
- `GetWhereItems<TKey, TEntity, TQueryCriteria>()` - 获取WHERE条件项集合
- `GetOrderByItems<TKey, TEntity, TQueryCriteria>()` - 获取ORDER BY条件项集合

#### 直接构建SQL字符串
- `BuildWhereClause<TKey, TEntity, TQueryCriteria>()` - 构建WHERE条件SQL
- `BuildOrderByClause<TKey, TEntity, TQueryCriteria>()` - 构建ORDER BY条件SQL
- `BuildWhereAndOrderByClause<TKey, TEntity, TQueryCriteria>()` - 构建WHERE和ORDER BY条件SQL

#### 使用集合构建SQL的静态方法
- `SqlQueryBuilderExtensions.BuildWhereClause(List<WhereItem>, out parameters)` - 使用WhereItem集合构建WHERE条件SQL
- `SqlQueryBuilderExtensions.BuildOrderByClause(List<OrderByItem>)` - 使用OrderByItem集合构建ORDER BY条件SQL
- `SqlQueryBuilderExtensions.BuildWhereAndOrderByClause(whereItems, orderByItems, out parameters)` - 使用集合构建WHERE和ORDER BY条件SQL

### EntityFrameworkCore层构建器

- `WhereItemBuilder.BuildWhereClause()` - 构建WHERE条件SQL
- `OrderByItemBuilder.BuildOrderByClause()` - 构建ORDER BY条件SQL

### EntityFrameworkCore层工厂方法

#### WhereItemFactory
- `WhereItemFactory.Equal(field, value)` - 创建等于条件
- `WhereItemFactory.Contains(field, value)` - 创建包含条件
- `WhereItemFactory.GreaterThan(field, value)` - 创建大于条件
- `WhereItemFactory.Between(field, lower, upper)` - 创建范围条件
- `WhereItemFactory.In(field, values)` - 创建IN条件
- 等等...

#### OrderByItemFactory
- `OrderByItemFactory.Ascending(field)` - 创建升序排序
- `OrderByItemFactory.Descending(field)` - 创建降序排序
- `OrderByItemFactory.CreateMultiple(specs)` - 批量创建排序条件

## 特性

### 1. 参数化查询
所有查询值都通过参数化处理，防止SQL注入攻击。

### 2. 字段名映射
自动读取实体类的`[Column]`属性获取正确的数据库字段名。

### 3. 类型安全
编译时检查查询条件属性名与实体属性名的匹配。

### 4. 灵活组合
可以与任意复杂的基础SQL语句组合使用。

### 5. 架构清晰
严格的分层设计，避免循环引用，职责分离明确。

## 注意事项

1. **不构建完整SQL**：本系统只构建WHERE和ORDER BY部分，不构建完整的SQL查询语句
2. **架构约束**：EntityFrameworkCore层不能引用Application.Contract层
3. **字段映射**：确保查询条件类的属性名与实体类的属性名匹配
4. **参数命名**：SQL参数自动命名为@p0, @p1, @p2...

## 示例代码

完整的使用示例请参考：
- `Application.Contract/Examples/SqlQueryBuilderExample.cs`

## 扩展性

系统设计具有良好的扩展性：

1. **新增操作符**：在`WhereOperator`枚举中添加新值，并在`WhereItemBuilder`中实现对应逻辑
2. **自定义字段映射**：通过`[Column]`属性或扩展`GetDatabaseColumnName`方法
3. **复杂查询支持**：可以与任意复杂的基础SQL语句结合使用
