# SQL查询构建器使用指南

## 概述

由于EntityFrameworkCore在处理复杂SQL联查操作时的局限性，我们设计了一套分层的解决方案来构建WHERE条件和ORDER BY条件的SQL语句。

## 架构设计

### 分层结构

1. **Application.Contract层**：
   - `PagedQueryCriteria<T>` - 分页查询条件
   - `QueryOperatorAttribute` - 查询操作符属性
   - `QueryCriteriaToWhereItemExtensions` - 转换扩展方法
   - `SqlWhereItem` / `SqlOrderByItem` - SQL条件项类
   - `SqlWhereOperator` / `SqlOrderByDirection` - SQL操作符枚举

2. **EntityFrameworkCore层**：
   - `WhereItemBuilder` / `OrderByItemBuilder` - SQL构建器
   - `SqlQueryBuilderExtensions` - 综合扩展方法

### 设计原则

- **分离关注点**：Application.Contract层处理对象转换和定义SQL条件项，EntityFrameworkCore层处理SQL字符串生成
- **避免循环引用**：通过合理的依赖关系设计避免项目间的循环引用
- **通用性**：通过泛型和表达式实现最大的可重用性
- **类型安全**：使用反射读取Column属性确保数据库字段名正确
- **参数化查询**：所有SQL参数都经过参数化处理，防止SQL注入攻击

## 使用方法

### 1. 定义实体类

```csharp
public class UserEntity : BaseEntity<long>
{
    [Column("username")]
    public string Username { get; set; } = string.Empty;

    [Column("email")]
    public string Email { get; set; } = string.Empty;

    [Column("real_name")]
    public string RealName { get; set; } = string.Empty;

    [Column("status")]
    public int Status { get; set; }
}
```

### 2. 定义查询条件类

```csharp
public class UserQueryCriteria : BaseQueryCriteria
{
    [Equal]
    public string? Username { get; set; }

    [Contains]
    public string? RealName { get; set; }

    [Equal]
    public int? Status { get; set; }

    [GreaterThanOrEqual]
    public DateTime? CreatedTimeStart { get; set; }
}
```

### 3. 构建查询

```csharp
// 创建查询条件
var criteria = new PagedQueryCriteria<UserQueryCriteria>
{
    Condition = new UserQueryCriteria
    {
        Username = "admin",
        RealName = "管理员",
        Status = 1
    },
    PageParams = new PageRequestParams(1, 10),
    OrderBy = new[]
    {
        new OrderByRequestItem("CreatedTime", FieldSortDirection.Descending)
    }
};

// 基础查询SQL
var baseQuery = @"
    SELECT u.id, u.username, u.email, u.real_name, u.status
    FROM users u
    LEFT JOIN user_roles ur ON u.id = ur.user_id";

// 构建完整的分页查询
var sql = criteria.BuildPagedQuery<long, UserEntity, UserQueryCriteria>(
    baseQuery, 
    out var parameters);
```

### 4. 执行查询

```csharp
// 使用生成的SQL和参数执行查询
var command = connection.CreateCommand();
command.CommandText = sql;

foreach (var param in parameters)
{
    var parameter = command.CreateParameter();
    parameter.ParameterName = param.Key;
    parameter.Value = param.Value;
    command.Parameters.Add(parameter);
}

var result = await command.ExecuteReaderAsync();
```

## 支持的查询操作符

| 操作符 | 属性 | SQL示例 |
|--------|------|---------|
| 等于 | `[Equal]` | `field = @p0` |
| 不等于 | `[NotEqual]` | `field <> @p0` |
| 包含 | `[Contains]` | `field LIKE '%@p0%'` |
| 开始于 | `[StartsWith]` | `field LIKE '@p0%'` |
| 结束于 | `[EndsWith]` | `field LIKE '%@p0'` |
| 大于 | `[GreaterThan]` | `field > @p0` |
| 大于等于 | `[GreaterThanOrEqual]` | `field >= @p0` |
| 小于 | `[LessThan]` | `field < @p0` |
| 小于等于 | `[LessThanOrEqual]` | `field <= @p0` |
| 范围 | `[Between]` | `field BETWEEN @p0 AND @p1` |
| 在集合中 | `[In]` | `field IN (@p0, @p1, @p2)` |

## 可用的扩展方法

### SqlQueryBuilderExtensions

- `BuildCompleteQuery<TKey, TEntity, TQueryCriteria>()` - 构建完整查询
- `BuildPagedQuery<TKey, TEntity, TQueryCriteria>()` - 构建分页查询
- `BuildCountQuery<TKey, TEntity, TQueryCriteria>()` - 构建计数查询
- `BuildWhereClause<TKey, TEntity, TQueryCriteria>()` - 仅构建WHERE条件
- `BuildOrderByClause<TKey, TEntity, TQueryCriteria>()` - 仅构建ORDER BY条件

## 高级用法

### 复杂联表查询

```csharp
var complexQuery = @"
    SELECT DISTINCT 
        u.id, 
        u.username, 
        u.real_name,
        STRING_AGG(r.name, ',') as role_names
    FROM users u
    LEFT JOIN user_roles ur ON u.id = ur.user_id
    LEFT JOIN roles r ON ur.role_id = r.id
    GROUP BY u.id, u.username, u.real_name";

var sql = criteria.BuildPagedQuery<long, UserEntity, UserQueryCriteria>(
    complexQuery,
    out var parameters);
```

### 自定义字段映射

```csharp
public class UserQueryCriteria : BaseQueryCriteria
{
    // 使用PropertyName指定映射到不同的实体属性
    [Equal("CreatedTime")]
    public DateTime? CreateTime { get; set; }
}
```

## 注意事项

1. **字段名映射**：系统会自动读取实体类的`[Column]`属性来确定数据库字段名
2. **参数化查询**：所有值都会被参数化，防止SQL注入
3. **类型安全**：查询条件属性名必须与实体类属性名匹配
4. **性能考虑**：对于简单查询，建议继续使用EF Core的LINQ表达式
5. **复杂查询**：此方案主要用于EF Core无法处理的复杂联表查询

## 扩展性

如需添加新的查询操作符：

1. 在`WhereOperator`枚举中添加新值
2. 在`WhereItemBuilder.BuildSingleCondition`中添加对应的处理逻辑
3. 在`QueryCriteriaToWhereItemExtensions.ConvertToWhereOperator`中添加转换逻辑
4. 创建对应的属性类（如`[NewOperator]`）

## 测试

参考`SqlQueryBuilderExample.cs`中的示例代码进行测试和学习。
