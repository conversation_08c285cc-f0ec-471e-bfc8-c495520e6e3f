﻿<Project Sdk="Microsoft.NET.Sdk">


    <Import Project="..\..\..\Common.Secrets.props"/>
    <Import Project="..\..\..\Common.props"/>
    <ItemGroup>


        <PackageReference Include="Microsoft.EntityFrameworkCore.Proxies"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\..\core\abstractions\XJ.Framework.Library.Common.Abstraction\XJ.Framework.Library.Common.Abstraction.csproj"/>
        <ProjectReference Include="..\..\core\abstractions\XJ.Framework.Library.Logging.Abstraction\XJ.Framework.Library.Logging.Abstraction.csproj"/>
        <ProjectReference Include="..\XJ.Framework.Library.Domain\XJ.Framework.Library.Domain.csproj"/>
    </ItemGroup>

</Project>
