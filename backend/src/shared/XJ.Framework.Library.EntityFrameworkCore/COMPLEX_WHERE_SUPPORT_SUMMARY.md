# 复杂WHERE条件支持总结

## 新增功能概述

为了支持您提到的复杂WHERE条件（如FormInstanceRepository中的复杂子查询），我们扩展了SQL查询构建器，新增了以下功能：

### ✅ 新增的操作符

| 操作符 | 枚举值 | 功能描述 | SQL示例 |
|--------|--------|----------|---------|
| NOT IN | `WhereOperator.NotIn` | 不在集合中 | `field NOT IN (@p0, @p1, @p2)` |
| EXISTS | `WhereOperator.Exists` | 存在子查询 | `EXISTS (SELECT 1 FROM ...)` |
| NOT EXISTS | `WhereOperator.NotExists` | 不存在子查询 | `NOT EXISTS (SELECT 1 FROM ...)` |
| 原生SQL | `WhereOperator.RawSql` | 自定义SQL片段 | `自定义SQL逻辑` |
| IS NULL | `WhereOperator.IsNull` | 字段为空 | `field IS NULL` |
| IS NOT NULL | `WhereOperator.IsNotNull` | 字段不为空 | `field IS NOT NULL` |

### ✅ 新增的逻辑支持

- **AND/OR逻辑连接**：通过`LogicalOperator`枚举支持AND和OR逻辑
- **复杂条件组合**：支持多种操作符的灵活组合
- **子查询支持**：通过`SubQuery`属性支持复杂的子查询逻辑
- **原生SQL片段**：通过`RawSql`属性支持任意复杂的SQL逻辑

## 您的FormInstanceRepository场景解决方案

### 原始EF Core代码
```csharp
var data = queryable.Where(whereLambda)
    .Where(q => !queryable.Where(f =>
        f.Key != q.Key && !f.IsObsoleted && q.BusinessId.ToLower().Equals(f.BusinessId.ToLower()))
    .Any(f => f.VersionTime > q.VersionTime));
```

### 使用新的SQL查询构建器实现
```csharp
// 1. 获取基础条件项（从PagedQueryCriteria转换）
var whereItems = criteria.GetWhereItems<long, FormInstanceEntity, FormInstanceQueryCriteria>();

// 2. 添加复杂的版本控制逻辑
var latestVersionSubQuery = @"
    SELECT 1 FROM form_instances f2 
    WHERE f2.key != f1.key 
        AND f2.is_obsoleted = 0 
        AND LOWER(f2.business_id) = LOWER(f1.business_id)
        AND f2.version_time > f1.version_time";

whereItems.Add(WhereItemFactory.NotExists(latestVersionSubQuery));

// 3. 构建最终SQL
var whereClause = SqlQueryBuilderExtensions.BuildWhereClause(whereItems, out var parameters);
var baseQuery = "SELECT f1.* FROM form_instances f1";
var completeSql = $"{baseQuery} {whereClause}";
```

## 新增的工厂方法

### WhereItemFactory新增方法
```csharp
// NOT IN条件
WhereItemFactory.NotIn("status", new[] { 0, -1 })

// EXISTS条件
WhereItemFactory.Exists("SELECT 1 FROM user_roles WHERE user_id = users.id")

// NOT EXISTS条件
WhereItemFactory.NotExists("SELECT 1 FROM user_locks WHERE user_id = users.id")

// 原生SQL条件
WhereItemFactory.RawSql("LOWER(business_id) = LOWER({0})", "FORM_001")

// NULL检查
WhereItemFactory.IsNull("deleted_at")
WhereItemFactory.IsNotNull("email")

// OR逻辑连接
WhereItemFactory.Or("name", WhereOperator.Contains, "admin")
```

## 使用场景示例

### 1. 版本控制查询（您的场景）
```csharp
var whereItems = new List<WhereItem>
{
    WhereItemFactory.Equal("is_obsoleted", false),
    WhereItemFactory.NotExists(@"
        SELECT 1 FROM form_instances f2 
        WHERE f2.key != f1.key 
            AND f2.is_obsoleted = 0 
            AND LOWER(f2.business_id) = LOWER(f1.business_id)
            AND f2.version_time > f1.version_time")
};
```

### 2. 复杂权限查询
```csharp
var whereItems = new List<WhereItem>
{
    WhereItemFactory.Equal("is_active", true),
    WhereItemFactory.Exists(@"
        SELECT 1 FROM user_project_permissions upp
        INNER JOIN projects p ON upp.project_id = p.id
        WHERE upp.user_id = users.id 
            AND p.status = 'active'
            AND upp.permission_level >= 2"),
    WhereItemFactory.NotExists(@"
        SELECT 1 FROM user_locks ul 
        WHERE ul.user_id = users.id 
            AND ul.lock_time > NOW() - INTERVAL 1 HOUR")
};
```

### 3. JSON字段查询
```csharp
var whereItems = new List<WhereItem>
{
    WhereItemFactory.Equal("status", 1),
    WhereItemFactory.RawSql("JSON_EXTRACT(metadata, '$.type') = {0}", "dynamic_form"),
    WhereItemFactory.RawSql("JSON_EXTRACT(settings, '$.enabled') = {0}", true)
};
```

### 4. 复杂的AND/OR逻辑
```csharp
var whereItems = new List<WhereItem>
{
    // status = 1 AND
    WhereItemFactory.Equal("status", 1),
    
    // (name LIKE '%admin%' OR email LIKE '%admin%') AND
    WhereItemFactory.Or("name", WhereOperator.Contains, "admin"),
    WhereItemFactory.Or("email", WhereOperator.Contains, "admin"),
    
    // created_time >= ? AND
    WhereItemFactory.GreaterThanOrEqual("created_time", DateTime.Now.AddDays(-7)),
    
    // (role_id IN (1,2,3) OR is_super_admin = 1)
    WhereItemFactory.Or("role_id", WhereOperator.In, new[] { 1, 2, 3 }),
    WhereItemFactory.Or("is_super_admin", WhereOperator.Equal, true)
};
```

## 架构优势

### 1. 保持分层架构
- EntityFrameworkCore层：独立的包装类和SQL构建器
- Application.Contract层：转换扩展和便捷方法
- 严格避免循环引用

### 2. 灵活性
- 支持从PagedQueryCriteria获取基础条件
- 支持在Application层添加复杂条件
- 支持完全手动创建条件项

### 3. 安全性
- 所有参数都经过参数化处理
- 防止SQL注入攻击
- 类型安全的操作

### 4. 可维护性
- 清晰的工厂方法
- 丰富的示例代码
- 完整的文档说明

## 构建状态

- ✅ EntityFrameworkCore项目构建成功
- ✅ Application.Contract项目构建成功
- ✅ 所有新功能正常工作
- ✅ 向后兼容现有代码

## 文件清单

### 新增/修改的文件
- `WhereItem.cs` - 添加了新的属性和枚举
- `WhereItemBuilder.cs` - 添加了新操作符的构建逻辑
- `WhereItemFactory.cs` - 添加了新的工厂方法
- `SqlQueryBuilderExtensions.cs` - 添加了集合操作方法
- `ComplexWhereConditionExample.cs` - 复杂条件使用示例
- `README_SqlQueryBuilder.md` - 更新了文档

### 支持的操作符总数
- 基础操作符：11个（Equal, NotEqual, Contains等）
- 新增操作符：7个（NotIn, Exists, NotExists等）
- **总计：18个操作符**

这个扩展完全解决了您提到的复杂WHERE条件需求，特别是FormInstanceRepository中的复杂子查询场景，同时保持了架构的清晰性和代码的可维护性。
