using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Options;
using System.Net.Mime;
using System.Security.Authentication;
using XJ.Framework.Library.Common.Abstraction.Exceptions;
using XJ.Framework.Library.Domain.Shared.Enums;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using XJ.Framework.Rbac.Application.Contract;
using XJ.Framework.Rbac.Application.Contract.Interfaces;
using XJ.Framework.Rbac.Domain.Shared.Enums;

namespace XJ.Framework.Library.WebApi.Filters;

/// <summary>
/// 权限验证过滤器
/// </summary>
public class PermissionAuthorizationFilter : IAsyncAuthorizationFilter
{
    private readonly IAuthProvider _authProvider;
    private readonly IOptions<ApplicationOption> _applicationOption;


    public PermissionAuthorizationFilter(IAuthProvider authProvider,
        IOptions<ApplicationOption> applicationOption)
    {
        _authProvider = authProvider;
        _applicationOption = applicationOption;
    }

    private async Task WriteAuthenticationErrorAsync(AuthorizationFilterContext context, string message)
    {
        context.Result = new JsonResult(new
        {
            Code = StatusCodes.Status401Unauthorized,
            Message = message
        });
        context.HttpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
        await Task.CompletedTask;
    }

    private async Task WriteAuthorizationErrorAsync(AuthorizationFilterContext context, string message)
    {
        context.Result = new JsonResult(new
        {
            Code = StatusCodes.Status403Forbidden,
            Message = message
        });
        context.HttpContext.Response.StatusCode = StatusCodes.Status403Forbidden;
        await Task.CompletedTask;
    }

    private async Task SetCurrentUserAsync(AuthorizationFilterContext context)
    {
        UserProfileDto? user = null;
        try
        {
            user = await _authProvider.GetUserProfileAsync();
        }
        catch (Exception e)
        {
            await WriteAuthenticationErrorAsync(context, e.Message);
            return;
        }

        if (user == null)
        {
            await WriteAuthenticationErrorAsync(context, "用户未登录或登录已过期");
            return;
        }

        if (user!.Status != UserStatus.Enabled)
        {
            await WriteAuthenticationErrorAsync(context, "用户账号已被禁用或锁定");
            return;
        }

        context.HttpContext.Items["CurrentUser"] = user;
    }

    public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
    {
        // 1. 检查是否允许匿名访问
        var allowAnonymous = context.ActionDescriptor.EndpointMetadata
            .Any(em => em is AllowAnonymousAttribute);
        if (allowAnonymous)
        {
            return;
        }

        await SetCurrentUserAsync(context);

        await ValidatePermissionAsync(context);
    }

    private async Task ValidatePermissionAsync(AuthorizationFilterContext context)
    {
        try
        {
            // 4. 获取当前请求的控制器和操作信息
            if (context.ActionDescriptor is ControllerActionDescriptor actionDescriptor)
            {
                // 4.1 获取请求方法并转换为枚举
                var requestMethod = context.HttpContext.Request.Method;

                if (!Constants.StringToHttpMethodMap.TryGetValue(requestMethod, out var httpMethod))
                {
                    await WriteAuthorizationErrorAsync(context, "不支持的HTTP方法");
                }

                // 4.2 获取请求路径
                var path = actionDescriptor.AttributeRouteInfo!.Template;

                var publicPermission = actionDescriptor.EndpointMetadata
                    .OfType<PublicPermissionAttribute>()
                    .FirstOrDefault();
                //如果没有公共权限特性描述
                if (publicPermission == null)
                {
                    // 4.3 获取权限代码
                    var permissionCode = GetPermissionCode(actionDescriptor);

                    // 5. 检查用户是否有权限访问
                    var hasPermission = await _authProvider.ValidateApiPermissionAsync(
                        permissionCode,
                        httpMethod,
                        path,
                        _applicationOption.Value.ApplicationCode
                    );

                    if (!hasPermission)
                    {
                        await WriteAuthorizationErrorAsync(context,
                            $"没有访问权限,Path:{path},Method:{requestMethod},PermissionCode:{permissionCode}");
                    }
                }
            }
        }
        catch (Exception e)
        {
            await WriteAuthorizationErrorAsync(context, e.Message);
        }
    }

    private string GetPermissionCode(ControllerActionDescriptor actionDescriptor)
    {
        // 优先使用特性中指定的权限代码
        var requirePermission = actionDescriptor.EndpointMetadata
            .OfType<RequirePermissionAttribute>()
            .FirstOrDefault();

        if (requirePermission != null)
        {
            return requirePermission.PermissionCode;
        }

        // 如果没有指定权限代码，则使用默认的命名规则
        // var appCode = actionDescriptor.RouteValues["area"] ?? "default";
        var module = actionDescriptor.ControllerName.Replace("Controller", "").ToLower();
        var action = actionDescriptor.ActionName.ToLower();

        return $"{_applicationOption.Value.ApplicationCode}:api:{module}:{action}";
    }
}