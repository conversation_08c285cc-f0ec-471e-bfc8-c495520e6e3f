namespace XJ.Framework.Library.WebApi.Filters;

public class ParameterFilter : IParameterFilter
{
    public void Apply(OpenApiParameter parameter, ParameterFilterContext context)
    {
        if (!parameter.In.HasValue || parameter.In.Value != ParameterLocation.Query)
            return;

        var parameterName = parameter.Name;

        // Console.WriteLine(parameterName);

        var replacement = new Dictionary<string, string>
        {
            { "PageParams.", "$" },
            { "Condition.", "" }
        };

        replacement.ForEach(kv =>
        {
            if (parameterName.StartsWith(kv.Key))
            {
                parameter.Name = parameterName.Replace(kv.Key, kv.Value);
            }
        });

        if (parameterName == "OrderBy")
        {
            var schema = parameter.Schema;
            schema.Type = "object";
            schema.Properties.Add("$sortBy", new OpenApiSchema { Type = "string", Default = new OpenApiString("") });
            schema.Properties.Add("$orderBy", new OpenApiSchema { Type = "string", Default = new OpenApiString("") });


            // context.SchemaRepository.Schemas.Remove("OrderBy");
            // context.ApiParameterDescription
        }
    }
}