using XJ.Framework.Library.Application.Contract.OperationDtos;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using XJ.Framework.Library.EntityFrameworkCore.Repositories;

namespace XJ.Framework.Library.WebApi.Controllers;

[ApiController]
[Route("[controller]")]
public class BaseEditableAppController<TKey, TDto, TOperationDto, TService, TQueryCriteria> : ControllerBase
    where TService : IEditableAppService<TKey, TOperationDto>, IAppService<TKey, TDto, TQueryCriteria>
    where TKey : struct
    where TDto : BaseDto<TKey>
    where TOperationDto : BaseOperationDto
    where TQueryCriteria : BaseQueryCriteria
{
    protected readonly TService Service;
    protected readonly ICurrentUserContext CurrentUserContext;

    public BaseEditableAppController(IServiceProvider serviceProvider)
    {
        Service = serviceProvider.GetRequiredService<TService>();
        CurrentUserContext = serviceProvider.GetRequiredService<ICurrentUserContext>();
    }
}