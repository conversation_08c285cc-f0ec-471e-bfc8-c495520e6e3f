using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using System.Reflection;
using XJ.Framework.Library.Common.Abstraction.Configuration;
using XJ.Framework.Library.Common.Abstraction.JsonConverters;
using XJ.Framework.Library.Logging.Abstraction.DI;
using XJ.Framework.Library.WebApi.Binders;
using XJ.Framework.Library.WebApi.Filters;
using XJ.Framework.Library.WebApi.Services;

namespace XJ.Framework.Library.WebApi.Extensions;

public static class WebApiExtensions
{
    public static WebApplication Init<TAuthProvider, TAuthInfoGetter, TWrapper>(this WebApplicationBuilder builder)
        where TWrapper : WebApiWrapper, new()
        where TAuthProvider : class, IAuthProvider
        where TAuthInfoGetter : class, IAuthInfoGetter
    {
        var wrapper = new TWrapper();


        builder.AddBasicApplication<TAuth<PERSON>rovider, TAuthInfoGetter, TWrapper>(wrapper);

        var app = builder.Build();

        app.UseBasicApplication(wrapper);

        return app;
    }
}
