// global using 指令

global using Microsoft.AspNetCore.Builder;
global using Microsoft.Extensions.Configuration;
global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.Extensions.DependencyInjection.Extensions;
global using Microsoft.Extensions.Hosting;
global using XJ.Framework.Library.Common.Abstraction.Contexts;
global using XJ.Framework.Library.Logging.Abstraction.DI;
global using XJ.Framework.Library.WebApi.Binders;
global using XJ.Framework.Library.WebApi.Filters;