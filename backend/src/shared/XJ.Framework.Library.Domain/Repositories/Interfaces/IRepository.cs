using System.Linq.Expressions;
using XJ.Framework.Library.Domain.Common;
using XJ.Framework.Library.Domain.Entities;

namespace XJ.Framework.Library.Domain.Repositories.Interfaces;

/// <summary>
/// 基础仓储接口
/// </summary>
/// <typeparam name="TKey">主键类型</typeparam>
/// <typeparam name="TEntity">实体类型</typeparam>
public interface IRepository<TKey, TEntity>
    where TEntity : BaseEntity<TKey>
    where TKey : struct
{
    /// <summary>
    /// 获取实体查询对象
    /// </summary>
    IQueryable<TEntity> Entities { get; }

    #region 查找数据

    /// <summary>
    /// 根据条件判断是否存在
    /// </summary>
    /// <param name="predicate">查询条件</param>
    Task<bool> AnyAsync(Expression<Func<TEntity, bool>> predicate);

    /// <summary>
    /// 根据条件获取数量
    /// </summary>
    /// <param name="predicate">查询条件</param>
    Task<long> CountAsync(Expression<Func<TEntity, bool>> predicate);

    /// <summary>
    /// 根据条件获取单个实体
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <param name="isNoTracking">是否不跟踪实体</param>
    Task<TEntity?> GetAsync(Expression<Func<TEntity, bool>> predicate, bool isNoTracking = true);

    /// <summary>
    /// 根据主键获取单个实体
    /// </summary>
    /// <param name="id">主键</param>
    Task<TEntity?> GetAsync(TKey id);

    /// <summary>
    /// 获取查询对象
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <param name="isNoTracking">是否不跟踪实体</param>
    Task<IQueryable<TEntity>> LoadAsync(Expression<Func<TEntity, bool>> predicate, bool isNoTracking = true);

    /// <summary>
    /// 获取实体列表
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <param name="isNoTracking">是否不跟踪实体</param>
    Task<IEnumerable<TEntity>> GetListAsync(Expression<Func<TEntity, bool>> predicate, bool isNoTracking = true);

    #endregion

    #region 分页查询

    /// <summary>
    /// 分页查询
    /// </summary>
    /// <param name="whereLambda">查询条件</param>
    /// <param name="rowIndex">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="orderBy">排序条件</param>
    /// <param name="isNoTracking">是否不跟踪实体</param>
    Task<PageData<TKey, TEntity>> GetPageAsync(
        Expression<Func<TEntity, bool>> whereLambda,
        int rowIndex,
        int pageSize,
        List<OrderbyDirection<TEntity>> orderBy,
        bool isNoTracking = true);

    #endregion
} 