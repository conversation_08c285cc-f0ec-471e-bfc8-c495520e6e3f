{{ include '_shared.scriban' }}
namespace XJ.Framework.{{ domain_name }}.Domain.Repositories.Interfaces;

/// <summary>
/// {{ entity_name }} 仓储接口
/// </summary>
{{ if has_audit_fields }}
public interface I{{ entity_name }}Repository : IAuditRepository<{{ key_type }}, {{ entity_name }}Entity>
{
}
{{ else if has_soft_delete }}
public interface I{{ entity_name }}Repository : ISoftDeleteRepository<{{ key_type }}, {{ entity_name }}Entity>
{
}
{{ else }}
public interface I{{ entity_name }}Repository : IEditableRepository<{{ key_type }}, {{ entity_name }}Entity>
{
}
{{ end }}